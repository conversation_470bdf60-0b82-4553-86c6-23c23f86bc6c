/**
 * MindBookStore
 *
 * Central state management for the MindBook component.
 * Manages sheets, active sheet, and sheet creation.
 *
 * This is the top-level store in the hierarchy, similar to Excel's Workbook.
 * It contains multiple sheets (MindSheets) and manages the active sheet.
 */

import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { MindSheetContentType, SheetData } from './StoreTypes';

// Store state interface
interface MindBookState {
  // State
  sheets: SheetData[];
  activeSheetId: string | null;
  name: string | null;
  currentMindBookId: string | null; // Track the current MindBook ID for save/update operations

  // Actions
  setSheets: (sheets: SheetData[]) => void;
  setActiveSheet: (sheetId: string) => void;
  setName: (name: string | null) => void;
  setCurrentMindBookId: (id: string | null) => void;
  createSheet: (title: string, contentType: MindSheetContentType, content: any) => string;
  createMindMapSheet: (title: string, mbcpData: any) => string;
  createChatForkSheet: (title: string, content: any) => string;
  removeSheet: (sheetId: string) => void;
  updateSheetContent: (sheetId: string, content: any) => void;
  updateSheetTitle: (sheetId: string, title: string) => void;
  saveSheetState: (sheetId: string, state: any) => void;
  getSheetState: (sheetId: string) => any;
  isNewMindBook: () => boolean; // Check if this is a new unsaved MindBook
}

// Create the store
export const useMindBookStore = create<MindBookState>((set, get) => ({
  // Initial state
  sheets: [],
  activeSheetId: null,
  name: null,
  currentMindBookId: null,

  // Actions
  setSheets: (sheets) => set({ sheets }),
  setName: (name) => set({ name }),
  setCurrentMindBookId: (id) => set({ currentMindBookId: id }),

  setActiveSheet: (sheetId) => set({ activeSheetId: sheetId }),

  createSheet: (title, contentType, content) => {
    const newSheet: SheetData = {
      id: uuidv4(),
      title,
      contentType,
      content
    };

    set(state => ({
      sheets: [...state.sheets, newSheet],
      activeSheetId: newSheet.id
    }));

    return newSheet.id;
  },

  createMindMapSheet: (title, mbcpData) => {
    return get().createSheet(title, MindSheetContentType.MINDMAP, mbcpData);
  },

  createChatForkSheet: (title, content) => {
    return get().createSheet(title, MindSheetContentType.CHATFORK, content);
  },

  removeSheet: (sheetId) => {
    set(state => {
      const newSheets = state.sheets.filter(sheet => sheet.id !== sheetId);

      // If we're removing the active sheet, set a new active sheet
      let newActiveSheetId = state.activeSheetId;
      if (state.activeSheetId === sheetId) {
        newActiveSheetId = newSheets.length > 0 ? newSheets[0].id : null;
      }

      return {
        sheets: newSheets,
        activeSheetId: newActiveSheetId
      };
    });
  },

  updateSheetContent: (sheetId, content) => {
    set(state => ({
      sheets: state.sheets.map(sheet =>
        sheet.id === sheetId ? { ...sheet, content } : sheet
      )
    }));
  },

  updateSheetTitle: (sheetId, title) => {
    set(state => ({
      sheets: state.sheets.map(sheet =>
        sheet.id === sheetId ? { ...sheet, title } : sheet
      )
    }));
  },

  saveSheetState: (sheetId, state) => {
    // Skip saving if state is empty or invalid
    if (!state || !Object.keys(state).length) {
      console.log('MindBookStore: Skipping save for empty state, sheet:', sheetId);
      return;
    }

    // Check if the sheet exists
    const sheet = get().sheets.find(s => s.id === sheetId);
    if (!sheet) {
      console.warn('MindBookStore: Cannot save state for non-existent sheet:', sheetId);
      return;
    }

    // Check if the state has actually changed
    const currentState = sheet.state;
    if (currentState && JSON.stringify(currentState) === JSON.stringify(state)) {
      console.log('MindBookStore: State unchanged, skipping save for sheet:', sheetId);
      return;
    }

    console.log('MindBookStore: Saving state for sheet:', sheetId);
    set(prevState => ({
      sheets: prevState.sheets.map(sheet =>
        sheet.id === sheetId ? { ...sheet, state } : sheet
      )
    }));
  },

  getSheetState: (sheetId) => {
    const sheet = get().sheets.find(s => s.id === sheetId);
    return sheet?.state || null;
  },

  isNewMindBook: () => {
    const state = get();
    return !state.currentMindBookId || state.currentMindBookId === null;
  }
}));
