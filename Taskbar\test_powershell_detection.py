#!/usr/bin/env python3
"""
Test script to find PowerShell processes that might be running run_setup.ps1
"""

import psutil

def find_powershell_processes():
    """Find PowerShell processes and check if any are running run_setup.ps1"""
    powershell_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            proc_name = proc_info['name'].lower()
            cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
            
            # Look for PowerShell processes
            if proc_name in ['powershell.exe', 'pwsh.exe']:
                is_mindback = 'run_setup.ps1' in cmdline
                powershell_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_name,
                    'is_mindback': is_mindback,
                    'cmdline': cmdline[:150] + '...' if len(cmdline) > 150 else cmdline
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return powershell_processes

def main():
    print("PowerShell Process Scanner")
    print("=" * 50)
    
    processes = find_powershell_processes()
    
    if not processes:
        print("✅ No PowerShell processes found")
        return
    
    mindback_processes = [p for p in processes if p['is_mindback']]
    other_processes = [p for p in processes if not p['is_mindback']]
    
    if mindback_processes:
        print(f"🎯 Found {len(mindback_processes)} MindBack PowerShell process(es):")
        for proc in mindback_processes:
            print(f"   PID: {proc['pid']} - {proc['name']}")
            print(f"   Command: {proc['cmdline']}")
            print()
    
    if other_processes:
        print(f"ℹ️  Found {len(other_processes)} other PowerShell process(es):")
        for proc in other_processes:
            print(f"   PID: {proc['pid']} - {proc['name']}")
            print(f"   Command: {proc['cmdline']}")
            print()
    
    print("=" * 50)
    if mindback_processes:
        print("💡 The stop function should target the MindBack PowerShell processes above.")
    else:
        print("💡 No MindBack PowerShell processes found. The stop function will target individual server processes.")

if __name__ == "__main__":
    main()
