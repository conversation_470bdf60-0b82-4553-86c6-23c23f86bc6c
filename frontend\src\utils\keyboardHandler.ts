/**
 * keyboardHandler.ts
 *
 * Global keyboard handler for the application.
 * This utility provides a centralized way to handle keyboard shortcuts.
 */

import { useMindMapStore } from '../core/state/MindMapStore';
import RegistrationManager, { EventType } from '../core/services/RegistrationManager';

// Initialize the keyboard handler
export const initKeyboardHandler = () => {
  console.log('Initializing global keyboard handler');

  // Handle keydown events
  const handleKeyDown = (e: KeyboardEvent) => {
    // Skip if focus is in an input or textarea
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement
    ) {
      return;
    }

    console.log('Global key handler:', e.key, 'Shift:', e.shiftKey);

    const { selectedNodeId, nodes, addNode } = useMindMapStore.getState();

    // DISABLED: Tab key handling moved to MindMapCanvas to prevent conflicts
    // Tab key functionality is now handled in MindMapCanvas.tsx
    if (e.key === 'Tab' && selectedNodeId) {
      console.log('keyboardHandler.ts: Tab key detected but delegating to MindMapCanvas handler');
      // Let the event bubble up to MindMapCanvas
      return;

          // Register the node opening event
          RegistrationManager.registerEvent(EventType.NODE_OPENED, { id: childId });

          console.log('Global handler: Opening NodeBox for new node:', childId);

          // After a short delay, focus and select the title text
          setTimeout(() => {
            const titleInput = document.querySelector('.nodebox-title-input') as HTMLInputElement;
            if (titleInput) {
              titleInput.focus();
              titleInput.select();
              console.log('Global handler: Selected title text in NodeBox');
            } else {
              console.log('Global handler: Could not find title input field');
            }
          }, 300);
        }
      }
    }
  };

  // Add the event listener
  window.addEventListener('keydown', handleKeyDown, true);

  // Return a cleanup function
  return () => {
    window.removeEventListener('keydown', handleKeyDown, true);
  };
};
