# MindBack Data Storage Guide

## Overview

MindBack stores all your data locally in your browser using **localStorage**. This provides complete privacy and offline functionality, but it's important to understand how this works and how to protect your data.

## 📍 Where is Your Data Stored?

### Browser localStorage
- **Location**: Your browser's local storage (Chrome, Firefox, Safari, etc.)
- **Path**: Not directly accessible as files - managed by the browser
- **Privacy**: Data never leaves your device - completely private
- **Offline**: Works without internet connection

### Storage Structure

MindBack organizes your data into several categories:

#### 🗂️ MindBooks
- **Pattern**: `mindbook_[name]_[timestamp]`
- **Contains**: Your saved projects with all sheets and content
- **Example**: `mindbook_my_project_1703123456789`

#### ⚙️ Context Settings  
- **Pattern**: `context_settings_[id]`
- **Contains**: Your foundational, strategic, and operational context configurations
- **Reusable**: Can be shared across multiple MindBooks

#### 📊 Sheet States
- **Pattern**: `mindmap_sheet_[sheet_id]`
- **Contains**: Individual sheet data (mindmaps, chatforks, etc.)
- **Temporary**: Automatically managed by the application

#### 🔧 System Data
- **Keys**: `active_mindbook`, `mindbooks_list`, etc.
- **Contains**: Application settings and metadata

## ✅ Advantages of localStorage

- **🔒 Complete Privacy**: Data never uploaded to servers
- **⚡ Fast Performance**: Instant loading and saving
- **🌐 Offline Ready**: Works without internet
- **🆓 No Account Required**: No sign-up or login needed
- **💾 Automatic Saving**: Changes saved as you work

## ⚠️ Important Limitations

### Browser-Specific Storage
- Data is tied to the specific browser you're using
- Chrome data won't appear in Firefox, and vice versa
- Different browser profiles have separate storage

### Device-Specific Storage
- Data doesn't sync across devices
- Your laptop data won't appear on your phone
- Each device maintains its own separate storage

### Storage Limits
- **Typical Limit**: 5-10MB per domain (varies by browser)
- **Current Usage**: Check in Data Management dialog
- **What Happens**: Browser may prompt to clear data when limit reached

### Data Loss Risks
- **Browser Cache Clearing**: Will delete all MindBack data
- **Incognito/Private Mode**: Data not saved permanently
- **Browser Uninstall**: May remove all stored data
- **System Cleanup Tools**: Can clear browser data

## 🛡️ Protecting Your Data

### Regular Backups
1. **Use Export Feature**: 
   - Go to ☰ Menu → Data Management → Export/Import
   - Download JSON backup files regularly
   - Store backups in cloud storage or external drives

2. **Save Important Projects**:
   - Use "Save As" to create multiple versions
   - Export individual MindBooks when complete
   - Keep copies of critical work

### Best Practices
- **Don't rely solely on browser storage** for important work
- **Export data before** clearing browser cache or updating browser
- **Test restore process** with exported data occasionally
- **Use descriptive names** for MindBooks to identify them easily

## 🔧 Data Management Tools

### Built-in Data Management
Access via: **☰ Menu → Data Management**

#### Overview Tab
- View storage statistics
- See total data usage
- Understand storage breakdown

#### Details Tab  
- Examine individual storage items
- See file sizes and modification dates
- Identify large data items

#### Export/Import Tab
- **Export All Data**: Download complete backup
- **Import Data**: Restore from backup file
- **Clear All Data**: Reset application (⚠️ Dangerous!)

### Storage Monitoring
- Monitor storage usage percentage
- Get warnings when approaching limits
- Clean up old or unused data

## 🚨 Emergency Data Recovery

### If Data Appears Lost
1. **Check Browser**: Ensure you're using the same browser and profile
2. **Check Import**: Try importing from recent backup
3. **Check Auto-save**: Look for `mindbook_autosave` in storage
4. **Avoid Cache Clearing**: Don't clear browser data until recovery attempted

### If Browser Crashes
1. **Restart Browser**: Data should still be there
2. **Check Data Management**: Verify data integrity
3. **Export Immediately**: Create backup after recovery

## 📊 Understanding Storage Usage

### Typical Sizes
- **Small MindBook**: 10-50 KB (few sheets, minimal content)
- **Medium MindBook**: 100-500 KB (multiple sheets, rich content)  
- **Large MindBook**: 1-5 MB (many sheets, extensive content)
- **Context Settings**: 5-20 KB each

### Storage Optimization
- **Delete Unused MindBooks**: Remove old projects you don't need
- **Clean Up Context Settings**: Remove duplicate or unused contexts
- **Export and Delete**: Archive old projects as files, remove from browser

## 🔄 Migration and Transfer

### Moving to New Browser
1. **Export Data** from old browser
2. **Install MindBack** in new browser
3. **Import Data** using exported file
4. **Verify** all data transferred correctly

### Moving to New Device
1. **Export Data** from old device
2. **Transfer File** via email, cloud storage, or USB
3. **Import Data** on new device
4. **Test Functionality** to ensure everything works

## 🆘 Support and Troubleshooting

### Common Issues
- **Data Not Appearing**: Check browser and profile
- **Storage Full**: Export and delete old data
- **Import Failing**: Verify file format and size
- **Performance Slow**: Check storage usage and clean up

### Getting Help
- Use Data Management dialog to diagnose issues
- Export data before troubleshooting
- Check browser console for error messages
- Contact support with specific error details

---

## Quick Reference

| Action | Location | Purpose |
|--------|----------|---------|
| View Storage Stats | ☰ → Data Management → Overview | Monitor usage |
| Export Backup | ☰ → Data Management → Export/Import | Create backup |
| Import Data | ☰ → Data Management → Export/Import | Restore backup |
| Clear All Data | ☰ → Data Management → Export/Import | Reset app |
| Save MindBook | ☰ → Save | Create permanent save |

**Remember**: Your data is only as safe as your backup strategy! 🔒
