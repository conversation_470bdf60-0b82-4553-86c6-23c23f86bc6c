"""
Test script to verify the launcher dependencies and basic functionality
"""

def test_imports():
    """Test if all required modules can be imported"""
    try:
        import tkinter as tk
        print("✅ tkinter - OK")
    except ImportError as e:
        print(f"❌ tkinter - FAILED: {e}")
        return False
    
    # Note: requests is no longer required for basic functionality
    print("✅ requests - Not needed (using socket-based port checking)")
    
    try:
        import psutil
        print("✅ psutil - OK")
    except ImportError as e:
        print(f"❌ psutil - FAILED: {e}")
        return False
    
    try:
        import subprocess
        print("✅ subprocess - OK")
    except ImportError as e:
        print(f"❌ subprocess - FAILED: {e}")
        return False
    
    try:
        import threading
        print("✅ threading - OK")
    except ImportError as e:
        print(f"❌ threading - FAILED: {e}")
        return False
    
    return True

def test_paths():
    """Test if required files exist"""
    from pathlib import Path
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    setup_script = project_root / "run_setup.ps1"
    
    print(f"\nScript directory: {script_dir}")
    print(f"Project root: {project_root}")
    print(f"Setup script: {setup_script}")
    
    if setup_script.exists():
        print("✅ run_setup.ps1 - Found")
        return True
    else:
        print("❌ run_setup.ps1 - NOT FOUND")
        return False

def test_basic_functionality():
    """Test basic launcher functionality without GUI"""
    try:
        import socket

        # Test backend port checking (should fail since servers aren't running)
        try:
            with socket.create_connection(("127.0.0.1", 8000), timeout=1):
                print("⚠️  Backend server appears to be running")
        except (socket.error, socket.timeout):
            print("✅ Backend port check - Working (no server running)")

        # Test frontend port checking (should fail since servers aren't running)
        try:
            with socket.create_connection(("127.0.0.1", 5173), timeout=1):
                print("⚠️  Frontend server appears to be running")
        except (socket.error, socket.timeout):
            print("✅ Frontend port check - Working (no server running)")

        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("MindBack Launcher - Dependency Test")
    print("=" * 40)
    
    print("\n1. Testing imports...")
    imports_ok = test_imports()
    
    print("\n2. Testing paths...")
    paths_ok = test_paths()
    
    print("\n3. Testing basic functionality...")
    func_ok = test_basic_functionality()
    
    print("\n" + "=" * 40)
    if imports_ok and paths_ok and func_ok:
        print("✅ ALL TESTS PASSED - Launcher should work!")
        print("\nYou can now run: python launch_mindback.py")
    else:
        print("❌ SOME TESTS FAILED - Please fix the issues above")
        
        if not imports_ok:
            print("\nTo fix import issues, run:")
            print("pip install requests psutil")
    
    input("\nPress Enter to exit...")
