/**
 * QuickSnapshotTest.js
 * 
 * Simple JavaScript test to generate snapshots without any backend dependencies.
 * Can be run directly in browser console.
 */

// Quick snapshot generation function
window.quickSnapshot = async function() {
  try {
    console.log('🔄 Generating quick snapshot...');
    
    // Get basic app state
    const snapshot = {
      id: `quick_snapshot_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: 'quick_test',
      
      // Collect localStorage data
      localStorage_data: {},
      
      // Basic app state
      app_state: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      },
      
      // Try to get MindBook state if available
      mindbook_state: null,
      
      // DOM information
      dom_info: {
        title: document.title,
        activeElement: document.activeElement?.tagName || 'none',
        inputElements: document.querySelectorAll('input, textarea').length,
        hasGovernanceBox: !!document.querySelector('[class*="governance"]'),
        hasMindSheets: !!document.querySelector('[class*="mindsheet"]')
      }
    };
    
    // Collect localStorage data
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('mindbook') || key.includes('context') || key.includes('mindsheet'))) {
        try {
          const value = localStorage.getItem(key);
          snapshot.localStorage_data[key] = JSON.parse(value);
        } catch {
          snapshot.localStorage_data[key] = value;
        }
      }
    }
    
    // Try to get MindBook state from global store
    try {
      if (window.useMindBookStore) {
        snapshot.mindbook_state = window.useMindBookStore.getState();
      }
    } catch (error) {
      console.log('Could not access MindBook store:', error.message);
    }
    
    // Create filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const mindbookName = snapshot.mindbook_state?.name || 'untitled';
    const filename = `${mindbookName}_${timestamp}_quick.json`;
    
    // Save to localStorage for inspection
    const storageKey = `quick_snapshot_${Date.now()}`;
    localStorage.setItem(storageKey, JSON.stringify(snapshot, null, 2));
    
    // Create downloadable file
    const blob = new Blob([JSON.stringify(snapshot, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
    
    // Show notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10000;
      opacity: 0.95;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    notification.innerHTML = `📸 Quick snapshot saved: ${filename}`;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
    
    console.log('✅ Quick snapshot generated successfully!');
    console.log('📁 Saved to localStorage as:', storageKey);
    console.log('💾 Downloaded as:', filename);
    console.log('📊 Snapshot data:', snapshot);
    
    return {
      success: true,
      filename,
      storageKey,
      snapshot
    };
    
  } catch (error) {
    console.error('❌ Quick snapshot failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Function to check what content is available
window.checkQuickContent = function() {
  const info = {
    localStorage_keys: [],
    mindbook_available: false,
    dom_elements: {
      inputs: document.querySelectorAll('input, textarea').length,
      governance: !!document.querySelector('[class*="governance"]'),
      mindsheets: !!document.querySelector('[class*="mindsheet"]'),
      nodes: document.querySelectorAll('[class*="node"]').length
    }
  };
  
  // Check localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('mindbook') || key.includes('context') || key.includes('mindsheet'))) {
      info.localStorage_keys.push(key);
    }
  }
  
  // Check MindBook store
  try {
    if (window.useMindBookStore) {
      const state = window.useMindBookStore.getState();
      info.mindbook_available = true;
      info.mindbook_state = {
        name: state.name,
        sheets_count: state.sheets?.length || 0,
        active_sheet: state.activeSheetId
      };
    }
  } catch (error) {
    info.mindbook_error = error.message;
  }
  
  console.log('📋 Quick content check:', info);
  return info;
};

// Function to view quick snapshots
window.getQuickSnapshots = function() {
  const snapshots = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('quick_snapshot_')) {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const parsed = JSON.parse(data);
          snapshots.push({
            key,
            timestamp: parsed.timestamp,
            mindbook: parsed.mindbook_state?.name || 'unknown',
            size: data.length,
            localStorage_items: Object.keys(parsed.localStorage_data || {}).length
          });
        }
      } catch (error) {
        console.warn(`Failed to parse quick snapshot ${key}:`, error);
      }
    }
  }
  
  snapshots.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
  console.log('📸 Quick snapshots found:', snapshots);
  return snapshots;
};

// Function to clean up quick snapshots
window.cleanupQuickSnapshots = function() {
  let cleaned = 0;
  const keys = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('quick_snapshot_')) {
      keys.push(key);
    }
  }
  
  keys.forEach(key => {
    localStorage.removeItem(key);
    cleaned++;
  });
  
  console.log(`🧹 Cleaned up ${cleaned} quick snapshots`);
  return cleaned;
};

console.log('🚀 Quick Snapshot Test loaded! Available commands:');
console.log('  quickSnapshot() - Generate and download snapshot');
console.log('  checkQuickContent() - Check available content');
console.log('  getQuickSnapshots() - List saved snapshots');
console.log('  cleanupQuickSnapshots() - Remove saved snapshots');
