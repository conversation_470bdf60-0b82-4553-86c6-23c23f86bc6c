/**
 * DataManagementDialog Styles
 */

.data-management-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.data-management-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.data-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.data-management-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background-color: #e0e0e0;
  color: #333;
}

.data-management-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.tab-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background-color: #e9ecef;
  color: #333;
}

.tab-button.active {
  background-color: white;
  color: #007bff;
  border-bottom: 2px solid #007bff;
}

.data-management-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Overview Tab Styles */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.storage-summary h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.stat-label {
  font-weight: 500;
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.storage-info h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.info-section {
  margin-bottom: 25px;
}

.info-section h4 {
  margin: 0 0 10px 0;
  color: #555;
  font-size: 16px;
}

.info-section p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.info-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info-section li {
  margin: 5px 0;
  color: #666;
  line-height: 1.4;
}

/* Details Tab Styles */
.details-tab h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.storage-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.storage-item {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  background-color: #fafafa;
}

.storage-item.mindbook {
  border-left: 4px solid #28a745;
}

.storage-item.context_settings {
  border-left: 4px solid #007bff;
}

.storage-item.sheet_state {
  border-left: 4px solid #ffc107;
}

.storage-item.system {
  border-left: 4px solid #6c757d;
}

.storage-item.other {
  border-left: 4px solid #dc3545;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-key {
  font-family: monospace;
  font-weight: bold;
  color: #333;
  word-break: break-all;
}

.item-size {
  font-weight: bold;
  color: #666;
  background-color: #e9ecef;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.item-details {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #666;
}

.item-type {
  text-transform: capitalize;
  font-weight: 500;
}

.item-description {
  flex: 1;
}

.item-date {
  font-size: 12px;
  color: #999;
}

/* Export Tab Styles */
.export-tab {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.export-section,
.import-section,
.danger-section {
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.export-section {
  background-color: #f8f9fa;
}

.import-section {
  background-color: #e7f3ff;
}

.danger-section {
  background-color: #fff5f5;
  border-color: #fed7d7;
}

.export-section h3,
.import-section h3,
.danger-section h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.export-section p,
.import-section p,
.danger-section p {
  margin: 0 0 15px 0;
  color: #666;
  line-height: 1.5;
}

.export-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.export-button:hover {
  background-color: #218838;
}

.danger-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.danger-button:hover {
  background-color: #c82333;
}

.warning {
  font-size: 13px;
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 10px;
}
