/**
 * StandaloneSnapshotTest.ts
 * 
 * Test snapshot generation without backend dependency.
 * This helps verify snapshot creation works independently of the continuation workflow.
 */

import MemorySnapshotService from '../core/services/MemorySnapshotService';
import { useMindBookStore } from '../core/state/MindBookStore';

export class StandaloneSnapshotTest {
  
  /**
   * Generate a snapshot and save it locally (without backend)
   */
  static async generateLocalSnapshot(): Promise<{
    success: boolean;
    snapshot?: any;
    filename?: string;
    error?: string;
  }> {
    try {
      console.log('🧪 StandaloneSnapshotTest: Generating local snapshot...');
      
      // Get mindbook name
      const mindBookStore = useMindBookStore.getState();
      const mindbookName = mindBookStore.name || 'untitled';
      
      // Generate snapshot data
      const snapshotData = await MemorySnapshotService.collectMBCPSnapshot();
      
      // Add test metadata
      const enhancedSnapshot = {
        ...snapshotData,
        test_metadata: {
          generated_by: 'StandaloneSnapshotTest',
          mindbook_name: mindbookName,
          test_timestamp: new Date().toISOString(),
          has_sheets: mindBookStore.sheets?.length || 0,
          active_sheet: mindBookStore.activeSheetId,
          test_purpose: 'Verify snapshot generation works independently'
        }
      };
      
      // Create filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
      const filename = `${mindbookName}_${timestamp}_test.json`;
      
      // Save to localStorage for inspection
      const snapshotKey = `test_snapshot_${Date.now()}`;
      localStorage.setItem(snapshotKey, JSON.stringify(enhancedSnapshot, null, 2));
      
      // Also create a downloadable file
      const blob = new Blob([JSON.stringify(enhancedSnapshot, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      URL.revokeObjectURL(url);
      
      console.log('✅ StandaloneSnapshotTest: Snapshot generated successfully');
      console.log('📁 Saved to localStorage key:', snapshotKey);
      console.log('💾 Downloaded as:', filename);
      
      return {
        success: true,
        snapshot: enhancedSnapshot,
        filename
      };
      
    } catch (error) {
      console.error('❌ StandaloneSnapshotTest: Error generating snapshot:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Test if we have active content worth snapshotting
   */
  static checkActiveContent(): {
    hasContent: boolean;
    details: any;
  } {
    try {
      const mindBookStore = useMindBookStore.getState();
      
      const details = {
        mindbook_name: mindBookStore.name,
        total_sheets: mindBookStore.sheets?.length || 0,
        active_sheet_id: mindBookStore.activeSheetId,
        sheet_titles: mindBookStore.sheets?.map(s => s.title) || [],
        has_active_sheet: !!mindBookStore.activeSheetId,
        localStorage_keys: Object.keys(localStorage).filter(k => 
          k.includes('mindbook') || k.includes('mindsheet') || k.includes('context')
        ).length
      };
      
      const hasContent = details.total_sheets > 0 || details.localStorage_keys > 0;
      
      console.log('🔍 StandaloneSnapshotTest: Active content check:', {
        hasContent,
        ...details
      });
      
      return {
        hasContent,
        details
      };
      
    } catch (error) {
      console.error('❌ StandaloneSnapshotTest: Error checking active content:', error);
      return {
        hasContent: false,
        details: { error: error.message }
      };
    }
  }
  
  /**
   * Simulate the Enter key snapshot trigger
   */
  static async simulateEnterKeySnapshot(): Promise<void> {
    console.log('⌨️ StandaloneSnapshotTest: Simulating Enter key snapshot...');
    
    // Check if we have content
    const contentCheck = this.checkActiveContent();
    
    if (!contentCheck.hasContent) {
      console.warn('⚠️ No active content found for snapshot');
      console.log('💡 Try creating a mindsheet first, then run this test');
      return;
    }
    
    // Generate snapshot
    const result = await this.generateLocalSnapshot();
    
    if (result.success) {
      console.log('🎉 Enter key snapshot simulation successful!');
      
      // Show notification like the real AutoSnapshotService would
      this.showTestNotification(`Test snapshot: ${result.filename}`);
    } else {
      console.error('💥 Enter key snapshot simulation failed:', result.error);
    }
  }
  
  /**
   * Show a test notification
   */
  private static showTestNotification(message: string): void {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #2196F3;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10000;
      opacity: 0.95;
      transition: opacity 0.3s ease;
      border-left: 4px solid #1976D2;
    `;
    notification.innerHTML = `🧪 ${message}`;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }
  
  /**
   * Get all test snapshots from localStorage
   */
  static getTestSnapshots(): Array<{
    key: string;
    timestamp: string;
    mindbook: string;
    size: number;
  }> {
    const testSnapshots = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('test_snapshot_')) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const parsed = JSON.parse(data);
            testSnapshots.push({
              key,
              timestamp: parsed.test_metadata?.test_timestamp || 'unknown',
              mindbook: parsed.test_metadata?.mindbook_name || 'unknown',
              size: data.length
            });
          }
        } catch (error) {
          console.warn(`Failed to parse test snapshot ${key}:`, error);
        }
      }
    }
    
    return testSnapshots.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
  }
  
  /**
   * Clean up test snapshots from localStorage
   */
  static cleanupTestSnapshots(): number {
    let cleaned = 0;
    const keys = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('test_snapshot_')) {
        keys.push(key);
      }
    }
    
    keys.forEach(key => {
      localStorage.removeItem(key);
      cleaned++;
    });
    
    console.log(`🧹 Cleaned up ${cleaned} test snapshots from localStorage`);
    return cleaned;
  }
  
  /**
   * View a specific test snapshot
   */
  static viewTestSnapshot(key: string): any {
    try {
      const data = localStorage.getItem(key);
      if (!data) {
        console.error(`Test snapshot not found: ${key}`);
        return null;
      }
      
      const parsed = JSON.parse(data);
      console.log(`📄 Test Snapshot: ${key}`, parsed);
      return parsed;
    } catch (error) {
      console.error(`Error viewing test snapshot ${key}:`, error);
      return null;
    }
  }
}

// Add to global scope for easy testing
(window as any).testSnapshot = StandaloneSnapshotTest.simulateEnterKeySnapshot;
(window as any).generateSnapshot = StandaloneSnapshotTest.generateLocalSnapshot;
(window as any).checkContent = StandaloneSnapshotTest.checkActiveContent;
(window as any).getTestSnapshots = StandaloneSnapshotTest.getTestSnapshots;
(window as any).cleanupTestSnapshots = StandaloneSnapshotTest.cleanupTestSnapshots;
(window as any).viewTestSnapshot = StandaloneSnapshotTest.viewTestSnapshot;
