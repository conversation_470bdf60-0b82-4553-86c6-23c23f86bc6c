@echo off
echo ========================================
echo    MindBack Taskbar Launcher Setup
echo ========================================
echo.

REM Change to the Taskbar directory
cd /d "%~dp0"

echo [1/4] Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    echo Please make sure Python and pip are installed
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

echo [2/4] Testing launcher functionality...
python test_launcher.py
if %errorlevel% neq 0 (
    echo ❌ Launcher test failed
    pause
    exit /b 1
)
echo.

echo [3/4] Converting logo to ICO format...
python convert_logo_to_ico.py
if %errorlevel% neq 0 (
    echo ⚠️  Logo conversion failed, but continuing...
)
echo.

echo [4/4] Creating desktop shortcut...
powershell -ExecutionPolicy Bypass -File "create_shortcut.ps1"
if %errorlevel% neq 0 (
    echo ❌ Failed to create shortcut
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ SETUP COMPLETE!
echo ========================================
echo.
echo 🚀 What you got:
echo   • Simple GUI launcher with server status
echo   • Real-time monitoring (red/green indicators)
echo   • Start/Stop server controls
echo   • Direct browser opening
echo   • Clean shutdown functionality
echo.
echo 📌 Next steps:
echo   1. Find "MindBack Launcher" shortcut on Desktop
echo   2. Right-click it → "Pin to taskbar"
echo   3. Click taskbar button to launch GUI
echo   4. Use GUI to start/stop MindBack servers
echo.
echo 🧪 Quick test: Run "python launch_mindback.py" in this folder
echo.
pause