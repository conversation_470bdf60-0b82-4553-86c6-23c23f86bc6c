import React, { useState, useRef, useCallback } from 'react';
import Paper, { PaperProps } from '@mui/material/Paper';
import Draggable from 'react-draggable';
import { LayoutSelector } from '../../layouts/components/LayoutSelector';
import { LayoutStrategyType } from '../../layouts/types';
import './EnhancedMindMapManager.css';
import { mindMapGovernance } from '../../../../core/governance/MindMapGovernance';

// Draggable Paper component for the manager
function DraggablePaper(props: PaperProps) {
  const nodeRef = useRef(null);

  return (
    <Draggable
      handle=".manager-dialog-header"
      bounds="body"
      nodeRef={nodeRef}
      defaultPosition={{x: 20, y: 60}}
      cancel=".manager-dialog-header-buttons"
      enableUserSelectHack={true}
      onStart={(e) => {
        // Ensure the drag start event doesn't get blocked
        e.stopPropagation();
        document.body.classList.add('react-draggable-transparent-selection');
      }}
      onStop={() => {
        document.body.classList.remove('react-draggable-transparent-selection');
      }}
    >
      <Paper {...props} ref={nodeRef} className="mindmap-manager-paper" />
    </Draggable>
  );
}

interface EnhancedMindMapManagerProps {
  open: boolean;
  onClose: () => void;
  sheetId: string; // Add sheetId to props
  store?: any; // Store prop passed from wrapper
}

// Function to convert between old and new direction formats
const convertLegacyDirectionToStrategy = (direction: string): LayoutStrategyType => {
  switch (direction) {
    case '0': return 'leftToRight';  // Right
    case '90': return 'topDown';     // Down
    case '180': return 'leftToRight'; // Left (still use leftToRight since it's just mirrored)
    case '270': return 'bottomUp';   // Up
    default: return 'leftToRight';
  }
};

// Helper to get layout display name with highlight for compact layout
const getLayoutDisplayName = (strategyType: LayoutStrategyType): JSX.Element | string => {
  if (strategyType === 'compactLeftToRight') {
    return (
      <span className="compact-layout-label">
        Compact Layout
        <span className="new-badge">NEW!</span>
      </span>
    );
  }

  const strategyNames: Record<LayoutStrategyType, string> = {
    'leftToRight': 'Left to Right',
    'topDown': 'Top Down',
    'bottomUp': 'Bottom Up',
    'radial': 'Radial',
    'compactLeftToRight': 'Compact Layout' // Fallback plain text
  };

  return strategyNames[strategyType];
};

export const EnhancedMindMapManager: React.FC<EnhancedMindMapManagerProps> = ({
  open,
  onClose,
  sheetId,
  store
}) => {
  // Check governance rules for manager behavior
  const config = mindMapGovernance.getSheetConfig(sheetId);
  const managerConfig = mindMapGovernance.getManagerConfig(sheetId);

  // Don't render if governance says no manager
  if (!managerConfig.show) {
    return null;
  }

  // CRITICAL FIX: ALL useState hooks MUST be declared FIRST, before any other hooks
  // State - ALL useState hooks must be declared at the top level first
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [size, setSize] = useState({ width: 320, height: 400 });
  const [showSettings, setShowSettings] = useState(false);
  const [logoSrc, setLogoSrc] = useState('./Public/Logo/MB_logo.jpg');

  // MOVED: Check for single instance rule - useEffect AFTER useState
  React.useEffect(() => {
    if (config.manager.behavior.singleInstanceOnly && open) {
      // Close any other manager instances for this sheet
      const existingManagers = document.querySelectorAll(`[data-manager-sheet="${sheetId}"]`);
      existingManagers.forEach((manager, index) => {
        if (index > 0) { // Keep only the first one
          console.log('EnhancedMindMapManager: Closing duplicate manager for sheet:', sheetId);
          const closeBtn = manager.querySelector('[data-close-button]');
          if (closeBtn) {
            (closeBtn as HTMLElement).click();
          }
        }
      });
    }
  }, [open, sheetId, config.manager.behavior.singleInstanceOnly]);

  // Refs - ALL useRef hooks must be declared after useState hooks
  const dialogRef = useRef<HTMLDivElement>(null);
  const resizeStartPos = useRef({ x: 0, y: 0 });
  const startSize = useRef({ width: 0, height: 0 });
  const isResizing = useRef(false);
  const storeRef = useRef(store);

  // Store the store reference in a ref to avoid re-renders
  if (store !== storeRef.current) {
    storeRef.current = store;
  }

  // Use useState to store values from the store
  const [storeValues, setStoreValues] = useState({
    nodes: {},
    connections: [],
    scale: 1,
    position: { x: 0, y: 0 },
    llmModel: 'gpt-3.5-turbo'
  });

  // Function to update store values - MUST be defined before useEffect
  const updateStoreValues = useCallback(() => {
    if (storeRef.current) {
      const state = storeRef.current.getState();
      setStoreValues({
        nodes: state.nodes || {},
        connections: state.connections || [],
        scale: state.scale || 1,
        position: state.position || { x: 0, y: 0 },
        llmModel: state.llmModel || 'gpt-3.5-turbo'
      });
    }
  }, []);

  // Update store values when the component mounts or the store changes
  React.useEffect(() => {
    if (storeRef.current) {
      // Initial update
      updateStoreValues();

      // Subscribe to store changes
      const unsubscribe = storeRef.current.subscribe(updateStoreValues);

      return () => {
        if (unsubscribe) unsubscribe();
      };
    }
  }, [updateStoreValues]);

  // Extract values from state
  const { nodes, connections, scale, position, llmModel } = storeValues;

  // Get functions from the store
  const updateConnection = useCallback((id: string, data: any) => {
    if (storeRef.current) {
      storeRef.current.getState().updateConnection(id, data);
    }
  }, []);

  const setLlmModel = useCallback((model: string) => {
    if (storeRef.current) {
      storeRef.current.getState().setLlmModel(model);
    }
  }, []);

  const setShowProjectDialog = useCallback((show: boolean) => {
    if (storeRef.current) {
      storeRef.current.getState().setShowProjectDialog(show);
    }
  }, []);

  // Get node and connection counts
  const nodeCount = Object.keys(nodes).length;
  const connectionCount = Array.isArray(connections) ? connections.length : 0;

  // Handle minimize/expand
  const handleMinimize = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  // Apply default settings to all connections
  const applyDefaultSettings = () => {
    if (!Array.isArray(connections)) return;

    connections.forEach(connection => {
      updateConnection(connection.id, {
        lineStyle: 'angled',
        thickness: 2,
        color: '#9ca3af',
        showArrow: false,
        type: 'solid'
      });
    });
  };

  // Add data attribute for governance tracking
  const managerProps = {
    'data-manager-sheet': sheetId,
    'data-manager-type': managerConfig.type
  };

  // Handle layout change requests from LayoutSelector
  const handleLayoutChange = async (strategy: LayoutStrategyType) => {
    if (!storeRef.current || !sheetId) {
      console.error('EnhancedMindMapManager: Missing store or sheetId for layout change');
      return;
    }

    try {
      console.log(`EnhancedMindMapManager: Layout change requested: ${strategy} for sheet ${sheetId}`);
      
      // Use the store's governance-integrated updateLayout method
      const storeState = storeRef.current.getState();
      const success = await storeState.updateLayout(strategy, 'user');
      
      if (success) {
        console.log(`EnhancedMindMapManager: Layout updated successfully to ${strategy}`);
      } else {
        console.warn(`EnhancedMindMapManager: Layout update failed for ${strategy}`);
      }
    } catch (error) {
      console.error('EnhancedMindMapManager: Error in handleLayoutChange:', error);
    }
  };

  // Handle layout rotation using the sheet-specific store
  const handleRotate = async () => {
    if (!storeRef.current || !sheetId) {
      console.error('EnhancedMindMapManager: Missing store or sheetId for rotation');
      return;
    }

    try {
      // Use layout strategies instead of direction rotation
      const strategies: LayoutStrategyType[] = ['leftToRight', 'topDown', 'bottomUp', 'radial'];
      
      // Get current layout strategy from store
      const storeState = storeRef.current.getState();
      const currentStrategy = storeState.currentLayoutStrategy || 'leftToRight';
      const currentIndex = strategies.indexOf(currentStrategy);
      const nextStrategy = strategies[(currentIndex + 1) % strategies.length];

      console.log(`EnhancedMindMapManager: Rotating layout from ${currentStrategy} to ${nextStrategy}`);

      // Apply the next layout strategy
      const success = await storeState.updateLayout(nextStrategy, 'user');
      
      if (success) {
        console.log(`EnhancedMindMapManager: Layout rotated successfully to ${nextStrategy}`);
      } else {
        console.warn(`EnhancedMindMapManager: Layout rotation failed to ${nextStrategy}`);
      }
    } catch (error) {
      console.error('EnhancedMindMapManager: Error in handleRotate:', error);
    }
  };

  // Zoom functions with governance validation
  const handleZoomIn = () => {
    if (storeRef.current) {
      const currentScale = storeRef.current.getState().scale;
      const newScale = Math.min(currentScale * 1.2, 2.0);
      
      if (mindMapGovernance.validateViewportChange(sheetId, { scale: newScale })) {
        storeRef.current.getState().setScale(newScale);
      }
    }
  };

  const handleZoomOut = () => {
    if (storeRef.current) {
      const currentScale = storeRef.current.getState().scale;
      const newScale = Math.max(currentScale / 1.2, 0.3);
      
      if (mindMapGovernance.validateViewportChange(sheetId, { scale: newScale })) {
        storeRef.current.getState().setScale(newScale);
      }
    }
  };

  // DISABLED: Competing centering system - let UnifiedLayoutManager handle centering
  const centerView = () => {
    console.log('EnhancedMindMapManager: 🚫 Manual centering DISABLED - UnifiedLayoutManager handles positioning');

    // Instead, trigger a layout refresh which will handle centering
    if (storeRef.current) {
      const currentState = storeRef.current.getState();
      if (currentState.rootNodeId) {
        // Trigger layout refresh through UnifiedLayoutManager
        import('../../../../core/layout/UnifiedLayoutManager').then(({ UnifiedLayoutManager }) => {
          UnifiedLayoutManager.getInstance().updateLayout({
            sheetId,
            strategy: currentState.currentLayoutStrategy || 'leftToRight',
            rootNodeId: currentState.rootNodeId,
            forceUpdate: true
          });
        });
      }
    }
  };

  // Handle LLM model change
  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (storeRef.current) {
      storeRef.current.getState().setLlmModel(e.target.value as any);
    }
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    isResizing.current = true;
    resizeStartPos.current = { x: e.clientX, y: e.clientY };
    startSize.current = { ...size };

    // Add the transparent selection class to the body
    document.body.classList.add('react-draggable-transparent-selection');

    const handleMouseMove = (e: MouseEvent) => {
      if (isResizing.current) {
        const deltaX = e.clientX - resizeStartPos.current.x;
        const deltaY = e.clientY - resizeStartPos.current.y;

        setSize({
          width: Math.max(280, startSize.current.width + deltaX),
          height: Math.max(200, startSize.current.height + deltaY)
        });
      }
    };

    const handleMouseUp = () => {
      isResizing.current = false;
      // Remove the transparent selection class from the body
      document.body.classList.remove('react-draggable-transparent-selection');
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleResetPosition = () => {
    // Simple reset by forcing a re-render
    if (dialogRef.current) {
      const draggableNode = dialogRef.current.closest('.react-draggable');
      if (draggableNode) {
        (draggableNode as HTMLElement).style.transform = 'translate(20px, 60px)';
      }
    }
  };

  const handleResetSize = () => {
    setSize({ width: 320, height: 400 });
  };

  return (
    <div
      className="mindmap-manager-dialog-root"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: 'none',
        zIndex: 1300,
        display: open ? 'block' : 'none'
      }}
    >
      <DraggablePaper
        style={{
          width: isCollapsed ? 'auto' : `${size.width}px`,
          height: isCollapsed ? 'auto' : `${size.height}px`,
          maxWidth: 'none',
          maxHeight: 'none',
          margin: 0,
          position: 'absolute',
          pointerEvents: 'auto'
        }}
        elevation={24}
        className="mindmap-manager-paper"
      >
        <div className="mindmap-manager-dialog" ref={dialogRef}>
          {/* Manager Dialog Header */}
          <div className="manager-dialog-header">
            <div className="manager-dialog-header-title">
              <img
                src={logoSrc}
                alt="MindBack Logo"
                className="manager-dialog-header-logo"
                width="24"
                height="24"
                draggable="false"
                onError={(e) => {
                  console.error('Failed to load logo:', logoSrc);
                  setLogoSrc('./Public/Logo/mindback_logo.jpg');
                }}
              />
              <span className="manager-dialog-header-text">MindMap Manager</span>
            </div>
            <div className="manager-dialog-header-buttons">
              <button
                onClick={handleResetPosition}
                className="manager-dialog-header-button"
                title="Reset Position"
              >
                ↖
              </button>
              <button
                onClick={handleResetSize}
                className="manager-dialog-header-button"
                title="Reset Size"
              >
                ⊡
              </button>
              <button
                onClick={handleMinimize}
                className="manager-dialog-header-button"
                title={isCollapsed ? "Expand" : "Collapse"}
              >
                {isCollapsed ? "□" : "_"}
              </button>
              <button
                onClick={onClose}
                className="manager-dialog-close-button"
                title="Close"
              >
                ×
              </button>
            </div>
          </div>

          {!isCollapsed && (
            <div className="mindmap-manager-content">
              {/* Stats */}
              <div className="mindmap-stats-container">
                <div className="mindmap-stat-item">
                  <span className="stat-label">Nodes:</span>
                  <span className="stat-value">{nodeCount}</span>
                </div>
                <div className="mindmap-stat-item">
                  <span className="stat-label">Connections:</span>
                  <span className="stat-value">{connectionCount}</span>
                </div>
                <div className="mindmap-stat-item">
                  <span className="stat-label">Scale:</span>
                  <span className="stat-value">{scale.toFixed(2)}x</span>
                </div>
                <div className="mindmap-stat-item">
                  <span className="stat-label">Position:</span>
                  <span className="stat-value">({Math.round(position.x)}, {Math.round(position.y)})</span>
                </div>
              </div>

              {/* View Controls */}
              <div className="control-group">
                <h4 className="control-group-title">Canvas Controls</h4>
                <div className="control-section">
                  <button
                    className="manager-control-button"
                    onClick={handleZoomIn}
                    title="Zoom In"
                  >
                    <span className="button-icon">+</span>
                    <span className="button-text">Zoom In</span>
                  </button>
                  <button
                    className="manager-control-button"
                    onClick={handleZoomOut}
                    title="Zoom Out"
                  >
                    <span className="button-icon">-</span>
                    <span className="button-text">Zoom Out</span>
                  </button>
                </div>
                <div className="control-section">
                  <button
                    className="manager-control-button"
                    onClick={centerView}
                    title="Center View"
                  >
                    <span className="button-icon">⌖</span>
                    <span className="button-text">Center View</span>
                  </button>
                  <button
                    className="manager-control-button"
                    onClick={handleRotate}
                    title="Rotate Layout"
                  >
                    <span className="button-icon">↻</span>
                    <span className="button-text">Rotate Layout</span>
                  </button>
                </div>
              </div>

              {/* Advanced Layout Controls */}
              <div className="control-group">
                <h4 className="control-group-title">Advanced Layout</h4>
                <div className="control-section-full">
                  <LayoutSelector
                    defaultStrategy={'leftToRight'}
                    store={storeRef.current}
                    sheetId={sheetId}
                    onStrategyChange={handleLayoutChange}
                  />
                </div>
              </div>

              {/* Project Controls */}
              <div className="control-group">
                <h4 className="control-group-title">Project Controls</h4>
                <div className="control-section">
                  <button
                    className="manager-control-button"
                    onClick={() => setShowProjectDialog(true)}
                    title="Open Projects"
                  >
                    <span className="button-icon">📂</span>
                    <span className="button-text">Open Project</span>
                  </button>
                  <button
                    className="manager-control-button"
                    onClick={() => setShowSettings(!showSettings)}
                    title="Settings"
                  >
                    <span className="button-icon">⚙️</span>
                    <span className="button-text">Settings</span>
                  </button>
                </div>
              </div>

              {showSettings && (
                <div className="settings-panel">
                  <h4 className="control-group-title">Connection Settings</h4>
                  <button
                    className="settings-button"
                    onClick={applyDefaultSettings}
                    title="Apply default style to all connections"
                  >
                    Apply Default Style
                  </button>

                  <h4 className="control-group-title">LLM Settings</h4>
                  <div className="llm-settings">
                    <label htmlFor="llm-model-select">Model:</label>
                    <select
                      id="llm-model-select"
                      value={llmModel}
                      onChange={handleModelChange}
                      className="llm-model-select"
                    >
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                      <option value="gpt-4">GPT-4</option>
                      <option value="gpt-4-mini">GPT-4 Mini</option>
                      <option value="claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                      <option value="claude-3.7-sonnet">Claude 3.7 Sonnet</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Resize handle */}
              <div
                className="manager-resize-handle"
                onMouseDown={handleResizeStart}
              />
            </div>
          )}
        </div>
      </DraggablePaper>
    </div>
  );
};
