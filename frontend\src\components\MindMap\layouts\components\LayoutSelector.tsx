import React, { useState } from 'react';
import { LayoutStrategyType, LayoutTypeUtils } from '../../../../core/types/LayoutTypes';
// REMOVED: LayoutGovernanceService (deleted conflicting system)
import './LayoutSelector.css';

interface LayoutSelectorProps {
  defaultStrategy?: LayoutStrategyType;
  onStrategyChange?: (strategy: LayoutStrategyType) => void;
  store?: any; // Accept store from parent component
  sheetId?: string; // Sheet ID for governance validation
}

/**
 * Component for selecting and applying different layout strategies
 * Now integrates with the governance system for validation
 */
export const LayoutSelector: React.FC<LayoutSelectorProps> = ({
  defaultStrategy = 'leftToRight',
  onStrategyChange,
  store,
  sheetId
}) => {
  const [selectedStrategy, setSelectedStrategy] = useState<LayoutStrategyType>(defaultStrategy);
  const [isApplying, setIsApplying] = useState(false);
  
  // Get available strategies from the unified type system
  const strategies: LayoutStrategyType[] = LayoutTypeUtils.getAllStrategies();
  
  // Strategy names for display - using the unified metadata
  const strategyNames: Record<LayoutStrategyType, React.ReactNode> = {};
  strategies.forEach(strategy => {
    const info = LayoutTypeUtils.getStrategyInfo(strategy);
    if (info) {
      strategyNames[strategy] = (
        <span className="layout-option">
          {info.icon && <span className="layout-icon">{info.icon}</span>}
          {info.displayName}
          {info.isExperimental && <span className="experimental-badge">BETA</span>}
        </span>
      );
    } else {
      // Fallback for strategies without metadata
      strategyNames[strategy] = strategy;
    }
  });
  
  // Handle strategy selection
  const handleStrategyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const strategy = e.target.value as LayoutStrategyType;
    setSelectedStrategy(strategy);
    
    if (onStrategyChange) {
      onStrategyChange(strategy);
    }
  };
  
  // Apply the selected layout strategy with governance validation
  const handleApplyLayout = async () => {
    if (!store || !sheetId) {
      console.error('LayoutSelector: Missing required props - store or sheetId');
      return;
    }

    if (isApplying) {
      console.log('LayoutSelector: Layout application already in progress');
      return;
    }

    setIsApplying(true);
    
    try {
      console.log(`LayoutSelector: Applying layout strategy: ${selectedStrategy} for sheet: ${sheetId}`);
      
      // REMOVED: LayoutGovernanceService validation (deleted conflicting system)
      // Layout changes are now allowed by default

      // Use the store's governance-integrated updateLayout method
      const storeState = store.getState();
      if (storeState.updateLayout) {
        console.log('LayoutSelector: Calling updateLayout with strategy:', selectedStrategy);
        
        const success = await storeState.updateLayout(selectedStrategy, 'user');
        
        if (success) {
          console.log(`LayoutSelector: Layout applied successfully: ${selectedStrategy}`);
          
          // Trigger callback if provided
          if (onStrategyChange) {
            onStrategyChange(selectedStrategy);
          }
        } else {
          console.warn(`LayoutSelector: Layout application failed for strategy: ${selectedStrategy}`);
          alert('Failed to apply layout. Please try again.');
        }
      } else {
        console.error('LayoutSelector: updateLayout function not found in store');
        alert('Layout system not available. Please refresh the page.');
      }
    } catch (error) {
      console.error('LayoutSelector: Error applying layout:', error);
      alert(`Error applying layout: ${error.message}`);
    } finally {
      setIsApplying(false);
    }
  };

  // Check if current strategy can be changed
  const canChangeLayout = (strategy: LayoutStrategyType): boolean => {
    // REMOVED: LayoutGovernanceService validation (deleted conflicting system)
    // All layout changes are now allowed by default
    return true;
  };

  // REMOVED: Get current preferred strategy from governance (deleted conflicting system)
  // No automatic strategy selection from governance
  
  return (
    <div className="layout-selector">
      <div className="layout-selector-header">
        <h4>Layout Strategy</h4>
        {sheetId && (
          <small className="sheet-indicator">Sheet: {sheetId.slice(-8)}</small>
        )}
      </div>
      
      <div className="layout-selector-controls">
        <select
          value={selectedStrategy}
          onChange={handleStrategyChange}
          className="layout-strategy-select"
          disabled={isApplying}
        >
          {strategies.map(strategy => {
            const info = LayoutTypeUtils.getStrategyInfo(strategy);
            const disabled = !canChangeLayout(strategy);
            
            return (
              <option 
                key={strategy} 
                value={strategy}
                disabled={disabled}
                title={disabled ? 'Layout change not allowed by governance' : info?.description}
              >
                {info?.displayName || strategy}
                {info?.isExperimental ? ' (Beta)' : ''}
                {disabled ? ' (Restricted)' : ''}
              </option>
            );
          })}
        </select>
        
        <button
          onClick={handleApplyLayout}
          className={`apply-layout-button ${isApplying ? 'applying' : ''}`}
          disabled={isApplying || !canChangeLayout(selectedStrategy)}
          title={
            !canChangeLayout(selectedStrategy) 
              ? 'Layout change not allowed by governance rules'
              : `Apply ${LayoutTypeUtils.getStrategyInfo(selectedStrategy)?.displayName} layout`
          }
        >
          {isApplying ? (
            <>
              <span className="spinner">⟳</span>
              Applying...
            </>
          ) : (
            'Apply Layout'
          )}
        </button>
      </div>
      
      {/* Layout strategy description */}
      {LayoutTypeUtils.getStrategyInfo(selectedStrategy) && (
        <div className="layout-description">
          <small>
            {LayoutTypeUtils.getStrategyInfo(selectedStrategy)?.description}
          </small>
        </div>
      )}
      
      {/* Governance status indicator */}
      {sheetId && !canChangeLayout(selectedStrategy) && (
        <div className="governance-warning">
          <small>⚠️ Layout changes restricted by governance rules</small>
        </div>
      )}
    </div>
  );
};
