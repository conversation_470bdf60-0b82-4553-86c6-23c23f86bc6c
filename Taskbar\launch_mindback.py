"""
MindBack Taskbar Launcher - Simplified Version
A clean, simple launcher for MindBack with server status indication and proper shutdown.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import time
import psutil
import os
import sys
from pathlib import Path

class MindBackLauncher:
    def __init__(self):
        # Get the project root directory (parent of Taskbar folder)
        self.project_root = Path(__file__).parent.parent
        self.setup_script = self.project_root / "run_setup.ps1"

        # Server status
        self.backend_running = False
        self.frontend_running = False
        self.server_processes = []

        # Create GUI
        self.setup_gui()

        # Start status monitoring
        self.monitor_servers()

    def setup_gui(self):
        """Create the simple GUI interface"""
        self.root = tk.Tk()
        self.root.title("MindBack Launcher")
        self.root.geometry("300x200")
        self.root.resizable(False, False)

        # Set icon if available
        icon_path = Path(__file__).parent / "MB_logo.ico"
        if icon_path.exists():
            self.root.iconbitmap(str(icon_path))

        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="MindBack Launcher",
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # Status indicators
        ttk.Label(main_frame, text="Backend Server:").grid(row=1, column=0, sticky=tk.W)
        self.backend_status = ttk.Label(main_frame, text="●", foreground="red")
        self.backend_status.grid(row=1, column=1, sticky=tk.E)

        ttk.Label(main_frame, text="Frontend Server:").grid(row=2, column=0, sticky=tk.W)
        self.frontend_status = ttk.Label(main_frame, text="●", foreground="red")
        self.frontend_status.grid(row=2, column=1, sticky=tk.E)

        # Buttons
        self.start_button = ttk.Button(main_frame, text="Start MindBack",
                                      command=self.start_servers)
        self.start_button.grid(row=3, column=0, columnspan=2, pady=(20, 5), sticky=(tk.W, tk.E))

        self.stop_button = ttk.Button(main_frame, text="Stop Servers",
                                     command=self.stop_servers, state="disabled")
        self.stop_button.grid(row=4, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        self.open_button = ttk.Button(main_frame, text="Open MindBack",
                                     command=self.open_mindback, state="disabled")
        self.open_button.grid(row=5, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

    def update_status(self, message):
        """Update the status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def check_server_status(self):
        """Check if servers are running by testing their ports without making HTTP requests"""
        import socket

        # Check backend (port 8000) - just test if port is open
        try:
            with socket.create_connection(("127.0.0.1", 8000), timeout=1):
                self.backend_running = True
        except (socket.error, socket.timeout):
            self.backend_running = False

        # Check frontend (port 5173) - just test if port is open
        try:
            with socket.create_connection(("127.0.0.1", 5173), timeout=1):
                self.frontend_running = True
        except (socket.error, socket.timeout):
            self.frontend_running = False

    def update_gui_status(self):
        """Update GUI based on server status"""
        # Update status indicators
        self.backend_status.config(
            foreground="green" if self.backend_running else "red"
        )
        self.frontend_status.config(
            foreground="green" if self.frontend_running else "red"
        )

        # Update button states
        servers_running = self.backend_running or self.frontend_running
        self.start_button.config(state="disabled" if servers_running else "normal")
        self.stop_button.config(state="normal" if servers_running else "disabled")
        self.open_button.config(state="normal" if self.frontend_running else "disabled")

    def monitor_servers(self):
        """Continuously monitor server status"""
        def monitor():
            while True:
                self.check_server_status()
                self.root.after(0, self.update_gui_status)
                time.sleep(5)  # Check every 5 seconds (reduced frequency)

        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

    def start_servers(self):
        """Start the MindBack servers"""
        def start():
            try:
                self.update_status("Starting MindBack servers...")

                # Change to project directory and run setup script
                cmd = [
                    "powershell", "-ExecutionPolicy", "Bypass",
                    "-File", str(self.setup_script)
                ]

                # Start the process but don't wait for it to complete
                process = subprocess.Popen(
                    cmd,
                    cwd=str(self.project_root),
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )

                self.server_processes.append(process)
                self.update_status("Servers starting... Please wait 10-20 seconds")

                # Wait a bit for servers to start
                time.sleep(5)

            except Exception as e:
                messagebox.showerror("Error", f"Failed to start servers: {str(e)}")
                self.update_status("Failed to start servers")

        start_thread = threading.Thread(target=start, daemon=True)
        start_thread.start()

    def stop_servers(self):
        """Stop the MindBack servers - simple and effective approach"""
        try:
            self.update_status("Stopping servers...")

            stopped_processes = []

            # Method 1: Terminate processes we started (PowerShell running run_setup.ps1)
            for process in self.server_processes:
                try:
                    if process.poll() is None:  # Process is still running
                        process.terminate()
                        try:
                            process.wait(timeout=3)
                            stopped_processes.append("Setup script")
                        except subprocess.TimeoutExpired:
                            process.kill()
                            stopped_processes.append("Setup script (force killed)")
                except:
                    pass

            # Clear our process list
            self.server_processes.clear()

            # Method 2: Find and kill PowerShell processes running run_setup.ps1
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''

                    # Kill PowerShell processes running our setup script
                    if proc_name in ['powershell.exe', 'pwsh.exe']:
                        if 'run_setup.ps1' in cmdline:
                            proc.terminate()
                            stopped_processes.append(f"PowerShell (PID: {proc_info['pid']})")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Method 3: Kill individual server processes directly
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''

                    should_stop = False

                    # Node.js processes (frontend - vite dev server)
                    if proc_name in ['node.exe', 'node']:
                        if any(keyword in cmdline.lower() for keyword in ['vite', 'dev', '5173', 'mindback-frontend']):
                            should_stop = True

                    # Python/uvicorn processes (backend)
                    elif proc_name in ['python.exe', 'python']:
                        if any(keyword in cmdline.lower() for keyword in ['uvicorn', 'main:app', '8000', 'mindback']):
                            should_stop = True

                    if should_stop:
                        proc.terminate()
                        stopped_processes.append(f"{proc_name} (PID: {proc_info['pid']})")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Wait a moment, then force kill any remaining processes
            import time
            time.sleep(2)

            # Force kill any stubborn processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''

                    should_kill = False
                    if proc_name in ['node.exe', 'node'] and any(k in cmdline.lower() for k in ['vite', '5173']):
                        should_kill = True
                    elif proc_name in ['python.exe', 'python'] and any(k in cmdline.lower() for k in ['uvicorn', '8000']):
                        should_kill = True
                    elif proc_name in ['powershell.exe', 'pwsh.exe'] and 'run_setup.ps1' in cmdline:
                        should_kill = True

                    if should_kill:
                        proc.kill()

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            if stopped_processes:
                self.update_status(f"Stopped: {', '.join(stopped_processes)}")
            else:
                self.update_status("Servers stopped")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop servers: {str(e)}")
            self.update_status("Error stopping servers")

    def open_mindback(self):
        """Open MindBack in the default browser"""
        try:
            import webbrowser
            webbrowser.open("http://localhost:5173")
            self.update_status("Opened MindBack in browser")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open browser: {str(e)}")

    def run(self):
        """Start the launcher"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_servers()

if __name__ == "__main__":
    # Check if required files exist
    launcher = MindBackLauncher()
    if not launcher.setup_script.exists():
        messagebox.showerror("Error", f"Setup script not found: {launcher.setup_script}")
        sys.exit(1)

    launcher.run()
