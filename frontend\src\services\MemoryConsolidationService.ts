/**
 * MemoryConsolidationService.ts
 * 
 * Provides a unified view of all memory data across the application.
 * Consolidates memory from local storage, backend, and snapshots.
 */

import { ChatMemoryService } from './ChatMemoryService';
import { MemoryAPI } from './api/MemoryAPI';
import MemorySnapshotService from '../core/services/MemorySnapshotService';
import { DataStorageManager } from '../core/services/DataStorageManager';

export interface ConsolidatedMemory {
  timestamp: string;
  sources: {
    local: LocalMemoryData;
    backend: BackendMemoryData;
    snapshots: SnapshotMemoryData;
    localStorage: LocalStorageData;
  };
  summary: MemorySummary;
  totalSize: number;
}

export interface LocalMemoryData {
  currentContext: any;
  conversationThreads: any[];
  recentMessages: any[];
  backendEnabled: boolean;
}

export interface BackendMemoryData {
  isAvailable: boolean;
  health?: any;
  memoryEntries: any[];
  conversationThreads: any[];
  error?: string;
}

export interface SnapshotMemoryData {
  latestSnapshot?: any;
  snapshotCount: number;
  totalSize: number;
}

export interface LocalStorageData {
  mindbooks: any[];
  contextSettings: any[];
  systemData: any[];
  totalItems: number;
  totalSize: number;
}

export interface MemorySummary {
  totalConversations: number;
  totalMessages: number;
  totalMemoryEntries: number;
  totalSnapshots: number;
  oldestEntry?: string;
  newestEntry?: string;
  memoryHealth: 'excellent' | 'good' | 'fair' | 'poor';
}

export class MemoryConsolidationService {
  private static instance: MemoryConsolidationService;
  
  static getInstance(): MemoryConsolidationService {
    if (!this.instance) {
      this.instance = new MemoryConsolidationService();
    }
    return this.instance;
  }

  /**
   * Get consolidated view of all memory data
   */
  async getConsolidatedMemory(): Promise<ConsolidatedMemory> {
    console.log('🧠 Consolidating memory from all sources...');
    
    const timestamp = new Date().toISOString();
    
    // Collect data from all sources in parallel
    const [localData, backendData, snapshotData, localStorageData] = await Promise.all([
      this.collectLocalMemoryData(),
      this.collectBackendMemoryData(),
      this.collectSnapshotMemoryData(),
      this.collectLocalStorageData()
    ]);

    // Calculate summary
    const summary = this.calculateMemorySummary(localData, backendData, snapshotData, localStorageData);
    
    // Calculate total size
    const totalSize = localStorageData.totalSize + snapshotData.totalSize;

    const consolidated: ConsolidatedMemory = {
      timestamp,
      sources: {
        local: localData,
        backend: backendData,
        snapshots: snapshotData,
        localStorage: localStorageData
      },
      summary,
      totalSize
    };

    console.log('🧠 Memory consolidation complete:', {
      totalConversations: summary.totalConversations,
      totalMessages: summary.totalMessages,
      totalSize: DataStorageManager.formatBytes(totalSize),
      memoryHealth: summary.memoryHealth
    });

    return consolidated;
  }

  /**
   * Collect local memory data from ChatMemoryService
   */
  private async collectLocalMemoryData(): Promise<LocalMemoryData> {
    try {
      const memoryService = ChatMemoryService.getInstance();
      const currentContext = memoryService.getCurrentContext();
      
      // Get all conversation threads (this would need to be exposed by ChatMemoryService)
      const conversationThreads: any[] = []; // TODO: Add method to get all threads
      
      return {
        currentContext,
        conversationThreads,
        recentMessages: currentContext.recentMessages || [],
        backendEnabled: memoryService.isBackendEnabled()
      };
    } catch (error) {
      console.error('Failed to collect local memory data:', error);
      return {
        currentContext: {},
        conversationThreads: [],
        recentMessages: [],
        backendEnabled: false
      };
    }
  }

  /**
   * Collect backend memory data
   */
  private async collectBackendMemoryData(): Promise<BackendMemoryData> {
    try {
      const isAvailable = await MemoryAPI.testConnectivity();
      
      if (!isAvailable) {
        return {
          isAvailable: false,
          memoryEntries: [],
          conversationThreads: [],
          error: 'Backend not available'
        };
      }

      const health = await MemoryAPI.checkHealth();
      
      // Get memory entries for current sheet (if available)
      const memoryEntries: any[] = []; // TODO: Get current sheet ID and fetch entries
      const conversationThreads: any[] = []; // TODO: Fetch conversation threads
      
      return {
        isAvailable: true,
        health,
        memoryEntries,
        conversationThreads
      };
    } catch (error) {
      return {
        isAvailable: false,
        memoryEntries: [],
        conversationThreads: [],
        error: error.message
      };
    }
  }

  /**
   * Collect snapshot memory data
   */
  private async collectSnapshotMemoryData(): Promise<SnapshotMemoryData> {
    try {
      // Get latest snapshot
      const latestSnapshot = await MemorySnapshotService.collectMBCPSnapshot();
      
      // Count snapshots in localStorage
      let snapshotCount = 0;
      let totalSize = 0;
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('mbcp_snapshot_')) {
          snapshotCount++;
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += new Blob([value]).size;
          }
        }
      }

      return {
        latestSnapshot,
        snapshotCount,
        totalSize
      };
    } catch (error) {
      console.error('Failed to collect snapshot data:', error);
      return {
        snapshotCount: 0,
        totalSize: 0
      };
    }
  }

  /**
   * Collect localStorage data
   */
  private async collectLocalStorageData(): Promise<LocalStorageData> {
    try {
      const storageInfo = DataStorageManager.getStorageInfo();
      const stats = DataStorageManager.getStorageStats();
      
      const mindbooks = storageInfo.filter(item => item.type === 'mindbook');
      const contextSettings = storageInfo.filter(item => item.type === 'context_settings');
      const systemData = storageInfo.filter(item => item.type === 'system');
      
      return {
        mindbooks: mindbooks.map(item => ({
          key: item.key,
          size: item.size,
          lastModified: item.lastModified,
          description: item.description
        })),
        contextSettings: contextSettings.map(item => ({
          key: item.key,
          size: item.size,
          lastModified: item.lastModified,
          description: item.description
        })),
        systemData: systemData.map(item => ({
          key: item.key,
          size: item.size,
          description: item.description
        })),
        totalItems: stats.totalKeys,
        totalSize: stats.totalSize
      };
    } catch (error) {
      console.error('Failed to collect localStorage data:', error);
      return {
        mindbooks: [],
        contextSettings: [],
        systemData: [],
        totalItems: 0,
        totalSize: 0
      };
    }
  }

  /**
   * Calculate memory summary statistics
   */
  private calculateMemorySummary(
    local: LocalMemoryData,
    backend: BackendMemoryData,
    snapshots: SnapshotMemoryData,
    localStorage: LocalStorageData
  ): MemorySummary {
    const totalConversations = local.conversationThreads.length + backend.conversationThreads.length;
    const totalMessages = local.recentMessages.length;
    const totalMemoryEntries = backend.memoryEntries.length;
    const totalSnapshots = snapshots.snapshotCount;

    // Determine memory health
    let memoryHealth: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
    
    if (backend.isAvailable && local.backendEnabled && totalMessages > 0) {
      memoryHealth = 'excellent';
    } else if (local.backendEnabled && totalMessages > 0) {
      memoryHealth = 'good';
    } else if (totalMessages > 0 || totalSnapshots > 0) {
      memoryHealth = 'fair';
    }

    // Find oldest and newest entries
    let oldestEntry: string | undefined;
    let newestEntry: string | undefined;
    
    const allDates: Date[] = [];
    
    // Add dates from localStorage items
    localStorage.mindbooks.forEach(item => {
      if (item.lastModified) {
        allDates.push(new Date(item.lastModified));
      }
    });
    
    if (allDates.length > 0) {
      allDates.sort((a, b) => a.getTime() - b.getTime());
      oldestEntry = allDates[0].toISOString();
      newestEntry = allDates[allDates.length - 1].toISOString();
    }

    return {
      totalConversations,
      totalMessages,
      totalMemoryEntries,
      totalSnapshots,
      oldestEntry,
      newestEntry,
      memoryHealth
    };
  }

  /**
   * Export all consolidated memory as JSON
   */
  async exportConsolidatedMemory(): Promise<string> {
    const consolidated = await this.getConsolidatedMemory();
    return JSON.stringify(consolidated, null, 2);
  }

  /**
   * Get memory health report
   */
  async getMemoryHealthReport(): Promise<{
    status: string;
    issues: string[];
    recommendations: string[];
    details: any;
  }> {
    const consolidated = await this.getConsolidatedMemory();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for issues
    if (!consolidated.sources.backend.isAvailable) {
      issues.push('Backend memory service is not available');
      recommendations.push('Check backend connectivity and restart memory service');
    }

    if (!consolidated.sources.local.backendEnabled) {
      issues.push('Local memory service has not connected to backend');
      recommendations.push('Initialize memory service backend connection');
    }

    if (consolidated.summary.totalMessages === 0) {
      issues.push('No conversation messages found');
      recommendations.push('Start a conversation to build memory context');
    }

    if (consolidated.summary.totalSnapshots === 0) {
      issues.push('No memory snapshots found');
      recommendations.push('Create memory snapshots for better context preservation');
    }

    // Determine overall status
    let status = 'healthy';
    if (issues.length > 2) {
      status = 'critical';
    } else if (issues.length > 0) {
      status = 'warning';
    }

    return {
      status,
      issues,
      recommendations,
      details: {
        memoryHealth: consolidated.summary.memoryHealth,
        totalSize: DataStorageManager.formatBytes(consolidated.totalSize),
        lastUpdated: consolidated.timestamp
      }
    };
  }
}

// Add to global scope for development
(window as any).getConsolidatedMemory = () => MemoryConsolidationService.getInstance().getConsolidatedMemory();
(window as any).getMemoryHealth = () => MemoryConsolidationService.getInstance().getMemoryHealthReport();
