import React from 'react';
import ReactDOM from 'react-dom/client';
import AppRefactored from './AppRefactored'; // Use the AppRefactored component with all fixes
import './index.css';

// Import governance and layout management system
import './core/governance/MindMapGovernance';
import './core/layout/UnifiedLayoutManager';
// REMOVED: import './core/services/LayoutGovernanceService'; (deleted conflicting system)
import './core/state/MindMapStoreFactory';
// REMOVED: import './core/utils/GovernanceTestUtils'; (deleted conflicting system)

// Import AutoSnapshotService to initialize snapshot functionality
import './services/AutoSnapshotService';

console.log('🔒 Governance system initialized');
console.log('🏗️ Unified layout management loaded');
console.log('🔧 Development utilities available');
console.log('📸 AutoSnapshot service initialized');

// Initialize React compatibility and polyfills
try {
  // Ensure React is available globally
  if (!window.React) {
    window.React = React;
    console.log('Set window.React from imported React');
  }
  
  // Add error handling for React internals
  if (!React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
    console.warn('React internals not available, using polyfill');
    React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
      ReactCurrentDispatcher: {
        current: {}
      }
    };
  }
  
  // Use the global compatibility function if available
  if (window.ReactCompatibilityUtils && typeof window.ReactCompatibilityUtils.initReactCompatibility === 'function') {
    console.log('Using global initReactCompatibility');
    window.ReactCompatibilityUtils.initReactCompatibility();
  } else {
    console.log('Global initReactCompatibility not available, using fallback');
    
    // Ensure React internals are properly set up
    const internals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    if (!internals.ReactCurrentDispatcher) {
      internals.ReactCurrentDispatcher = { current: {} };
    }
    if (!internals.ReactCurrentDispatcher.current) {
      internals.ReactCurrentDispatcher.current = {};
    }
    
    // Add useInternalStore if it doesn't exist
    const dispatcher = internals.ReactCurrentDispatcher.current;
    if (!dispatcher.useInternalStore) {
      dispatcher.useInternalStore = function(subscribe, getSnapshot) {
        return getSnapshot();
      };
    }
  }
  
  console.log('React compatibility initialized in main.tsx');
} catch (error) {
  console.error('Error initializing React compatibility:', error);
}

// Add error handling for uncaught errors
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
});

// Import ErrorBoundary
import ErrorBoundary from './components/ErrorBoundary';

// Create a fallback UI for critical errors
const FallbackUI = () => (
  <div style={{
    padding: '30px',
    margin: '50px auto',
    maxWidth: '600px',
    textAlign: 'center',
    backgroundColor: '#f8d7da',
    border: '1px solid #f5c6cb',
    borderRadius: '5px',
    color: '#721c24'
  }}>
    <h1>Application Error</h1>
    <p>The application encountered a critical error during startup.</p>
    <p>This might be due to compatibility issues with your browser or missing dependencies.</p>
    <button
      onClick={() => window.location.reload()}
      style={{
        marginTop: '20px',
        padding: '10px 20px',
        backgroundColor: '#dc3545',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
        fontSize: '16px'
      }}
    >
      Reload Application
    </button>
  </div>
);

// Render the app with error boundary
try {
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <ErrorBoundary
        componentName="Application Root"
        fallback={<FallbackUI />}
        onError={(error) => {
          console.error('Critical application error:', error);
        }}
      >
        <AppRefactored />
      </ErrorBoundary>
    </React.StrictMode>
  );
} catch (error) {
  console.error('Error rendering application:', error);
  
  // Fallback to direct DOM manipulation if React rendering fails
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="padding: 30px; margin: 50px auto; max-width: 600px; text-align: center; 
                  background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; color: #721c24;">
        <h1>Critical Error</h1>
        <p>The application could not be initialized.</p>
        <p>Please try refreshing the page or contact support if the issue persists.</p>
        <button onclick="window.location.reload()" 
                style="margin-top: 20px; padding: 10px 20px; background-color: #dc3545; color: white; 
                       border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
          Reload Application
        </button>
      </div>
    `;
  }
}
