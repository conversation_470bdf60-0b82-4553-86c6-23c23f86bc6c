"""
Memory API Routes

Provides REST API endpoints for MBCPMemoryService integration.
Implements Phase 1.1.1.1 of the Development Plan.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from datetime import datetime
import json

from ..services.mbcp_memory_service import MBCPMemoryService, MBCPMemoryEntry, MemoryType, ConversationThread
from ..services.snapshot_storage_service import SnapshotStorageService

# Initialize router
router = APIRouter(prefix="/api/memory", tags=["memory"])

# Initialize services (singleton pattern)
_memory_service: Optional[MBCPMemoryService] = None
_snapshot_service: Optional[SnapshotStorageService] = None

def get_memory_service() -> MBCPMemoryService:
    """Get or create memory service instance"""
    global _memory_service
    if _memory_service is None:
        _memory_service = MBCPMemoryService()
    return _memory_service

def get_snapshot_service() -> SnapshotStorageService:
    """Get or create snapshot service instance"""
    global _snapshot_service
    if _snapshot_service is None:
        _snapshot_service = SnapshotStorageService()
    return _snapshot_service

# Pydantic models for API requests/responses
class MemoryEntryRequest(BaseModel):
    type: str  # Will be converted to MemoryType enum
    sheet_id: Optional[str] = None
    parent_id: Optional[str] = None
    content: Dict[str, Any]
    metadata: Dict[str, Any] = {}
    relevance_score: float = 1.0

class MemoryEntryResponse(BaseModel):
    id: str
    type: str
    timestamp: str
    sheet_id: Optional[str]
    parent_id: Optional[str]
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    relevance_score: float

class ConversationThreadRequest(BaseModel):
    sheet_id: str
    parent_thread_id: Optional[str] = None
    context_summary: Optional[str] = None
    initial_message: Optional[Dict[str, Any]] = None

class ConversationThreadResponse(BaseModel):
    thread_id: str
    sheet_id: str
    parent_thread_id: Optional[str]
    context_summary: Optional[str]
    messages: List[Dict[str, Any]]
    created_at: str
    last_updated: str

class ParentContextResponse(BaseModel):
    thread_lineage: List[str]
    parent_messages: List[Dict[str, Any]]
    context_summary: Optional[str]

class MemoryBlockRequest(BaseModel):
    block_type: str  # 'CTX', 'MEM', or 'ZIPPED'
    sheet_id: Optional[str] = None
    thread_id: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None

class MemoryBlockResponse(BaseModel):
    type: str
    content: str
    metadata: Dict[str, Any]

class SnapshotRequest(BaseModel):
    mindbook_name: str
    snapshot_data: Dict[str, Any]

class SnapshotResponse(BaseModel):
    success: bool
    filename: str
    filepath: str
    message: str

class SnapshotListResponse(BaseModel):
    snapshots: List[Dict[str, Any]]
    total_count: int

# API Endpoints

@router.post("/entries", response_model=MemoryEntryResponse)
async def store_memory_entry(
    entry_request: MemoryEntryRequest,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Store a new memory entry"""
    try:
        # Convert string type to enum
        memory_type = MemoryType(entry_request.type.lower())
        
        # Create memory entry
        entry = MBCPMemoryEntry(
            id=f"mem_{int(datetime.now().timestamp() * 1000)}_{hash(str(entry_request.content)) % 10000}",
            type=memory_type,
            timestamp=datetime.now().isoformat(),
            sheet_id=entry_request.sheet_id,
            parent_id=entry_request.parent_id,
            content=entry_request.content,
            metadata=entry_request.metadata,
            relevance_score=entry_request.relevance_score
        )
        
        # Store the entry
        success = memory_service.store_memory_entry(entry)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to store memory entry")
        
        # Return the stored entry
        return MemoryEntryResponse(
            id=entry.id,
            type=entry.type.value,
            timestamp=entry.timestamp,
            sheet_id=entry.sheet_id,
            parent_id=entry.parent_id,
            content=entry.content,
            metadata=entry.metadata,
            relevance_score=entry.relevance_score
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid memory type: {entry_request.type}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error storing memory entry: {str(e)}")

@router.get("/entries/sheet/{sheet_id}", response_model=List[MemoryEntryResponse])
async def get_memory_entries_by_sheet(
    sheet_id: str,
    limit: int = 10,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Get memory entries for a specific sheet"""
    try:
        entries = memory_service.get_memory_entries_by_sheet(sheet_id, limit)
        
        return [
            MemoryEntryResponse(
                id=entry.id,
                type=entry.type.value,
                timestamp=entry.timestamp,
                sheet_id=entry.sheet_id,
                parent_id=entry.parent_id,
                content=entry.content,
                metadata=entry.metadata,
                relevance_score=entry.relevance_score
            )
            for entry in entries
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving memory entries: {str(e)}")

@router.post("/threads", response_model=ConversationThreadResponse)
async def create_conversation_thread(
    thread_request: ConversationThreadRequest,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Create a new conversation thread"""
    try:
        thread_id = memory_service.create_conversation_thread(
            sheet_id=thread_request.sheet_id,
            parent_thread_id=thread_request.parent_thread_id,
            context_summary=thread_request.context_summary
        )
        
        # Add initial message if provided
        if thread_request.initial_message:
            memory_service.add_message_to_thread(thread_id, thread_request.initial_message)
        
        # Get the created thread
        thread = memory_service.get_conversation_thread(thread_id)
        if not thread:
            raise HTTPException(status_code=500, detail="Failed to retrieve created thread")
        
        return ConversationThreadResponse(
            thread_id=thread.thread_id,
            sheet_id=thread.sheet_id,
            parent_thread_id=thread.parent_thread_id,
            context_summary=thread.context_summary,
            messages=thread.messages,
            created_at=thread.created_at,
            last_updated=thread.last_updated
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating conversation thread: {str(e)}")

@router.get("/threads/{thread_id}", response_model=ConversationThreadResponse)
async def get_conversation_thread(
    thread_id: str,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Get a conversation thread by ID"""
    try:
        thread = memory_service.get_conversation_thread(thread_id)
        if not thread:
            raise HTTPException(status_code=404, detail="Conversation thread not found")
        
        return ConversationThreadResponse(
            thread_id=thread.thread_id,
            sheet_id=thread.sheet_id,
            parent_thread_id=thread.parent_thread_id,
            context_summary=thread.context_summary,
            messages=thread.messages,
            created_at=thread.created_at,
            last_updated=thread.last_updated
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation thread: {str(e)}")

@router.post("/threads/{thread_id}/messages")
async def add_message_to_thread(
    thread_id: str,
    message: Dict[str, Any],
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Add a message to a conversation thread"""
    try:
        success = memory_service.add_message_to_thread(thread_id, message)
        if not success:
            raise HTTPException(status_code=404, detail="Conversation thread not found or failed to add message")
        
        return {"success": True, "message": "Message added to thread successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding message to thread: {str(e)}")

@router.get("/threads/{thread_id}/parent-context", response_model=ParentContextResponse)
async def get_parent_context(
    thread_id: str,
    depth: int = 3,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Get parent context for a conversation thread"""
    try:
        context = memory_service.get_parent_context(thread_id, depth)
        
        return ParentContextResponse(
            thread_lineage=context["thread_lineage"],
            parent_messages=context["parent_messages"],
            context_summary=context["context_summary"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving parent context: {str(e)}")

@router.post("/blocks/generate", response_model=MemoryBlockResponse)
async def generate_memory_block(
    block_request: MemoryBlockRequest,
    memory_service: MBCPMemoryService = Depends(get_memory_service)
):
    """Generate MBCP-compatible memory blocks for LLM requests"""
    try:
        # This endpoint will integrate with frontend ChatMemoryService logic
        # For now, we'll create a basic implementation that can be enhanced
        
        if block_request.block_type.upper() == 'CTX':
            # Generate context block
            content = f"sheet={block_request.sheet_id or 'unknown'}"
            if block_request.context_data:
                for key, value in block_request.context_data.items():
                    content += f"/{key}={value}"
            
            return MemoryBlockResponse(
                type='CTX',
                content=content,
                metadata={'generated_at': datetime.now().isoformat()}
            )
            
        elif block_request.block_type.upper() == 'MEM':
            # Generate memory block from thread context
            if not block_request.thread_id:
                return MemoryBlockResponse(
                    type='MEM',
                    content='No memory context available',
                    metadata={'generated_at': datetime.now().isoformat()}
                )
            
            # Get parent context
            context = memory_service.get_parent_context(block_request.thread_id)
            mem_parts = []
            
            if context['parent_messages']:
                mem_parts.append(f"THREAD: {len(context['parent_messages'])} parent messages")
            
            if context['context_summary']:
                mem_parts.append(f"CONTEXT: {context['context_summary']}")
            
            return MemoryBlockResponse(
                type='MEM',
                content='\n'.join(mem_parts) if mem_parts else 'No memory context available',
                metadata={'generated_at': datetime.now().isoformat(), 'thread_id': block_request.thread_id}
            )
            
        elif block_request.block_type.upper() == 'ZIPPED':
            # Generate compressed data block
            if block_request.sheet_id:
                entries = memory_service.get_memory_entries_by_sheet(block_request.sheet_id, 20)
                large_data = {
                    'sheet_id': block_request.sheet_id,
                    'memory_entries': [
                        {
                            'id': entry.id,
                            'type': entry.type.value,
                            'content': entry.content,
                            'timestamp': entry.timestamp
                        }
                        for entry in entries
                    ]
                }
                
                return MemoryBlockResponse(
                    type='ZIPPED',
                    content=f"[LARGE_CONTEXT]\n{json.dumps(large_data, indent=2)}\n[/LARGE_CONTEXT]",
                    metadata={'generated_at': datetime.now().isoformat(), 'data_size': len(json.dumps(large_data))}
                )
            
            return MemoryBlockResponse(
                type='ZIPPED',
                content='[LARGE_CONTEXT]\nNo data available\n[/LARGE_CONTEXT]',
                metadata={'generated_at': datetime.now().isoformat()}
            )
        
        else:
            raise HTTPException(status_code=400, detail=f"Invalid block type: {block_request.block_type}")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating memory block: {str(e)}")

@router.post("/snapshots", response_model=SnapshotResponse)
async def save_snapshot(
    snapshot_request: SnapshotRequest,
    snapshot_service: SnapshotStorageService = Depends(get_snapshot_service)
):
    """Save a snapshot to the backend/snapshots directory"""
    try:
        filepath = snapshot_service.save_snapshot(
            snapshot_request.mindbook_name,
            snapshot_request.snapshot_data
        )

        filename = filepath.split('/')[-1] if '/' in filepath else filepath.split('\\')[-1]

        return SnapshotResponse(
            success=True,
            filename=filename,
            filepath=filepath,
            message=f"Snapshot saved successfully as {filename}"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving snapshot: {str(e)}")

@router.get("/snapshots/{mindbook_name}", response_model=SnapshotListResponse)
async def get_snapshots(
    mindbook_name: str,
    snapshot_service: SnapshotStorageService = Depends(get_snapshot_service)
):
    """Get all snapshots for a specific mindbook"""
    try:
        snapshots = snapshot_service.get_snapshots_for_mindbook(mindbook_name)

        return SnapshotListResponse(
            snapshots=snapshots,
            total_count=len(snapshots)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving snapshots: {str(e)}")

@router.get("/snapshots/{mindbook_name}/latest")
async def get_latest_snapshot(
    mindbook_name: str,
    snapshot_service: SnapshotStorageService = Depends(get_snapshot_service)
):
    """Get the latest snapshot for a mindbook"""
    try:
        snapshot = snapshot_service.get_latest_snapshot(mindbook_name)

        if not snapshot:
            raise HTTPException(status_code=404, detail="No snapshots found for this mindbook")

        return snapshot

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving latest snapshot: {str(e)}")

@router.delete("/snapshots/{filename}")
async def delete_snapshot(
    filename: str,
    snapshot_service: SnapshotStorageService = Depends(get_snapshot_service)
):
    """Delete a specific snapshot file"""
    try:
        success = snapshot_service.delete_snapshot(filename)

        if not success:
            raise HTTPException(status_code=404, detail="Snapshot file not found")

        return {"success": True, "message": f"Snapshot {filename} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting snapshot: {str(e)}")

@router.get("/snapshots")
async def get_storage_stats(
    snapshot_service: SnapshotStorageService = Depends(get_snapshot_service)
):
    """Get snapshot storage statistics"""
    try:
        stats = snapshot_service.get_storage_stats()
        return stats

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting storage stats: {str(e)}")

@router.get("/health")
async def memory_health_check():
    """Health check for memory service"""
    try:
        memory_service = get_memory_service()
        snapshot_service = get_snapshot_service()
        storage_stats = snapshot_service.get_storage_stats()

        return {
            "status": "ok",
            "service": "MBCPMemoryService",
            "memory_entries": len(memory_service.memory_entries),
            "conversation_threads": len(memory_service.conversation_threads),
            "snapshots": storage_stats
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
