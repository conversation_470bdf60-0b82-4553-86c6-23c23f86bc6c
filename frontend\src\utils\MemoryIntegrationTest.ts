/**
 * MemoryIntegrationTest.ts
 * 
 * Test utility for validating memory integration functionality.
 * Implements Phase 1.1.1.4 of the Development Plan.
 */

import { ChatMemoryService } from '../services/ChatMemoryService';
import { MemoryAPI } from '../services/api/MemoryAPI';

export interface MemoryTestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

export class MemoryIntegrationTest {
  private static memoryService = ChatMemoryService.getInstance();

  /**
   * Run all memory integration tests
   */
  static async runAllTests(): Promise<MemoryTestResult[]> {
    console.log('🧪 Starting Memory Integration Tests...');
    
    const results: MemoryTestResult[] = [];
    
    // Test 1: Backend Connectivity
    results.push(await this.testBackendConnectivity());
    
    // Test 2: Memory Service Initialization
    results.push(await this.testMemoryServiceInitialization());
    
    // Test 3: Memory Block Generation
    results.push(await this.testMemoryBlockGeneration());
    
    // Test 4: Conversation Threading
    results.push(await this.testConversationThreading());
    
    // Test 5: Memory Context Integration
    results.push(await this.testMemoryContextIntegration());
    
    // Test 6: Backend Memory Storage
    results.push(await this.testBackendMemoryStorage());
    
    // Summary
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log(`🧪 Memory Integration Tests Complete: ${passed}/${total} passed`);
    
    if (passed === total) {
      console.log('✅ All memory integration tests passed!');
    } else {
      console.warn(`⚠️ ${total - passed} memory integration tests failed`);
    }
    
    return results;
  }

  /**
   * Test 1: Backend Connectivity
   */
  private static async testBackendConnectivity(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      const isConnected = await MemoryAPI.testConnectivity();
      const duration = Date.now() - startTime;
      
      if (isConnected) {
        const health = await MemoryAPI.checkHealth();
        return {
          testName: 'Backend Connectivity',
          success: true,
          message: 'Backend memory service is accessible',
          details: health,
          duration
        };
      } else {
        return {
          testName: 'Backend Connectivity',
          success: false,
          message: 'Backend memory service is not accessible',
          duration
        };
      }
    } catch (error) {
      return {
        testName: 'Backend Connectivity',
        success: false,
        message: `Backend connectivity test failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test 2: Memory Service Initialization
   */
  private static async testMemoryServiceInitialization(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      const initialized = await this.memoryService.initializeBackend();
      const duration = Date.now() - startTime;
      
      return {
        testName: 'Memory Service Initialization',
        success: initialized,
        message: initialized 
          ? 'Memory service initialized successfully' 
          : 'Memory service initialization failed',
        details: {
          backendEnabled: this.memoryService.isBackendEnabled()
        },
        duration
      };
    } catch (error) {
      return {
        testName: 'Memory Service Initialization',
        success: false,
        message: `Memory service initialization failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test 3: Memory Block Generation
   */
  private static async testMemoryBlockGeneration(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      // Add some test messages first
      this.memoryService.addStructuredMessage(
        'Test user message for memory block generation',
        'user',
        undefined,
        undefined,
        undefined,
        ['test'],
        'sent'
      );
      
      this.memoryService.addStructuredMessage(
        'Test LLM response for memory block generation',
        'llm',
        { type: 'factual', requiresMindmap: false },
        [],
        undefined,
        ['test'],
        'received'
      );
      
      // Test different block types
      const ctxBlock = await this.memoryService.generateEnhancedMemoryBlock('CTX');
      const memBlock = await this.memoryService.generateEnhancedMemoryBlock('MEM');
      const zippedBlock = await this.memoryService.generateEnhancedMemoryBlock('ZIPPED');
      
      const duration = Date.now() - startTime;
      
      const allBlocksGenerated = ctxBlock && memBlock && zippedBlock;
      
      return {
        testName: 'Memory Block Generation',
        success: allBlocksGenerated,
        message: allBlocksGenerated 
          ? 'All memory block types generated successfully' 
          : 'Failed to generate some memory blocks',
        details: {
          ctxBlock: {
            type: ctxBlock.type,
            contentLength: ctxBlock.content.length,
            source: ctxBlock.metadata.source
          },
          memBlock: {
            type: memBlock.type,
            contentLength: memBlock.content.length,
            source: memBlock.metadata.source
          },
          zippedBlock: {
            type: zippedBlock.type,
            contentLength: zippedBlock.content.length,
            source: zippedBlock.metadata.source
          }
        },
        duration
      };
    } catch (error) {
      return {
        testName: 'Memory Block Generation',
        success: false,
        message: `Memory block generation failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test 4: Conversation Threading
   */
  private static async testConversationThreading(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      // Create a test conversation thread
      const threadId = this.memoryService.createConversationThread('test_sheet_001');
      
      // Add messages to the thread
      this.memoryService.addStructuredMessage(
        'First message in thread',
        'user',
        undefined,
        undefined,
        undefined,
        ['test-thread'],
        'sent'
      );
      
      this.memoryService.addStructuredMessage(
        'Response to first message',
        'llm',
        { type: 'factual', requiresMindmap: false },
        [],
        undefined,
        ['test-thread'],
        'received'
      );
      
      // Test parent context retrieval
      const parentContext = await this.memoryService.getEnhancedParentContext(threadId);
      
      const duration = Date.now() - startTime;
      
      return {
        testName: 'Conversation Threading',
        success: !!threadId,
        message: threadId 
          ? 'Conversation threading works correctly' 
          : 'Failed to create conversation thread',
        details: {
          threadId,
          parentContext: {
            parentMessages: parentContext.parentMessages.length,
            threadLineage: parentContext.threadLineage.length,
            hasBackendContext: !!parentContext.backendContext
          }
        },
        duration
      };
    } catch (error) {
      return {
        testName: 'Conversation Threading',
        success: false,
        message: `Conversation threading test failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test 5: Memory Context Integration
   */
  private static async testMemoryContextIntegration(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      // Simulate a conversation with context
      this.memoryService.addStructuredMessage(
        'What is the capital of France?',
        'user',
        undefined,
        undefined,
        undefined,
        ['geography'],
        'sent'
      );
      
      this.memoryService.addStructuredMessage(
        'The capital of France is Paris.',
        'llm',
        { type: 'factual', requiresMindmap: false },
        [],
        undefined,
        ['geography'],
        'received'
      );
      
      this.memoryService.addStructuredMessage(
        'What about its population?',
        'user',
        undefined,
        undefined,
        undefined,
        ['geography'],
        'sent'
      );
      
      // Generate memory context that should include the previous conversation
      const memBlock = await this.memoryService.generateEnhancedMemoryBlock('MEM');
      
      const duration = Date.now() - startTime;
      
      // Check if the memory block contains context about Paris
      const hasContext = memBlock.content.toLowerCase().includes('paris') || 
                        memBlock.content.toLowerCase().includes('france') ||
                        memBlock.content.toLowerCase().includes('capital');
      
      return {
        testName: 'Memory Context Integration',
        success: hasContext,
        message: hasContext 
          ? 'Memory context includes conversation history' 
          : 'Memory context does not include expected conversation history',
        details: {
          memoryContent: memBlock.content,
          contentLength: memBlock.content.length,
          source: memBlock.metadata.source
        },
        duration
      };
    } catch (error) {
      return {
        testName: 'Memory Context Integration',
        success: false,
        message: `Memory context integration test failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test 6: Backend Memory Storage
   */
  private static async testBackendMemoryStorage(): Promise<MemoryTestResult> {
    const startTime = Date.now();
    
    try {
      if (!this.memoryService.isBackendEnabled()) {
        return {
          testName: 'Backend Memory Storage',
          success: false,
          message: 'Backend not enabled, skipping storage test',
          duration: Date.now() - startTime
        };
      }
      
      // Test storing a memory entry
      const testEntry = {
        type: 'conversation',
        sheet_id: 'test_sheet_002',
        content: {
          test: true,
          message: 'Test memory entry for integration testing',
          timestamp: new Date().toISOString()
        },
        metadata: {
          testRun: true,
          source: 'integration_test'
        },
        relevance_score: 1.0
      };
      
      const storedEntry = await MemoryAPI.storeMemoryEntry(testEntry);
      
      // Test retrieving memory entries
      const retrievedEntries = await MemoryAPI.getMemoryEntriesBySheet('test_sheet_002', 5);
      
      const duration = Date.now() - startTime;
      
      const testPassed = storedEntry && retrievedEntries.length > 0;
      
      return {
        testName: 'Backend Memory Storage',
        success: testPassed,
        message: testPassed 
          ? 'Backend memory storage and retrieval works correctly' 
          : 'Backend memory storage or retrieval failed',
        details: {
          storedEntry: storedEntry ? { id: storedEntry.id, type: storedEntry.type } : null,
          retrievedCount: retrievedEntries.length
        },
        duration
      };
    } catch (error) {
      return {
        testName: 'Backend Memory Storage',
        success: false,
        message: `Backend memory storage test failed: ${error.message}`,
        details: error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Quick test for development - runs essential tests only
   */
  static async runQuickTest(): Promise<boolean> {
    console.log('🚀 Running quick memory integration test...');
    
    const results = await Promise.all([
      this.testBackendConnectivity(),
      this.testMemoryServiceInitialization(),
      this.testMemoryBlockGeneration()
    ]);
    
    const allPassed = results.every(r => r.success);
    
    if (allPassed) {
      console.log('✅ Quick memory integration test passed!');
    } else {
      console.warn('❌ Quick memory integration test failed');
      results.forEach(r => {
        if (!r.success) {
          console.warn(`  - ${r.testName}: ${r.message}`);
        }
      });
    }
    
    return allPassed;
  }
}
