/**
 * MemoryAPI.ts
 * 
 * Frontend service for communicating with backend MBCPMemoryService.
 * Implements Phase 1.1.1.2 of the Development Plan.
 */

// API Types matching backend Pydantic models
export interface MemoryEntryRequest {
  type: string;
  sheet_id?: string;
  parent_id?: string;
  content: Record<string, any>;
  metadata?: Record<string, any>;
  relevance_score?: number;
}

export interface MemoryEntryResponse {
  id: string;
  type: string;
  timestamp: string;
  sheet_id?: string;
  parent_id?: string;
  content: Record<string, any>;
  metadata: Record<string, any>;
  relevance_score: number;
}

export interface ConversationThreadRequest {
  sheet_id: string;
  parent_thread_id?: string;
  context_summary?: string;
  initial_message?: Record<string, any>;
}

export interface ConversationThreadResponse {
  thread_id: string;
  sheet_id: string;
  parent_thread_id?: string;
  context_summary?: string;
  messages: Record<string, any>[];
  created_at: string;
  last_updated: string;
}

export interface ParentContextResponse {
  thread_lineage: string[];
  parent_messages: Record<string, any>[];
  context_summary?: string;
}

export interface MemoryBlockRequest {
  block_type: string; // 'CTX', 'MEM', or 'ZIPPED'
  sheet_id?: string;
  thread_id?: string;
  context_data?: Record<string, any>;
}

export interface MemoryBlockResponse {
  type: string;
  content: string;
  metadata: Record<string, any>;
}

export interface MemoryHealthResponse {
  status: string;
  service: string;
  memory_entries: number;
  conversation_threads: number;
  snapshots?: any;
}

export interface SnapshotRequest {
  mindbook_name: string;
  snapshot_data: Record<string, any>;
}

export interface SnapshotResponse {
  success: boolean;
  filename: string;
  filepath: string;
  message: string;
}

export interface SnapshotListResponse {
  snapshots: Array<{
    filename: string;
    filepath: string;
    mindbook_name: string;
    saved_at: string;
    version: number;
    file_size: number;
    created_at: string;
    modified_at: string;
  }>;
  total_count: number;
}

/**
 * MemoryAPI class for backend communication
 */
export class MemoryAPI {
  private static readonly BASE_URL = 'http://localhost:8000/api/memory';

  /**
   * Store a memory entry
   */
  static async storeMemoryEntry(entry: MemoryEntryRequest): Promise<MemoryEntryResponse> {
    try {
      console.log('MemoryAPI: Storing memory entry:', entry);
      
      const response = await fetch(`${this.BASE_URL}/entries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to store memory entry: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('MemoryAPI: Memory entry stored successfully:', result.id);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error storing memory entry:', error);
      throw error;
    }
  }

  /**
   * Get memory entries for a specific sheet
   */
  static async getMemoryEntriesBySheet(sheetId: string, limit: number = 10): Promise<MemoryEntryResponse[]> {
    try {
      console.log(`MemoryAPI: Getting memory entries for sheet ${sheetId}, limit ${limit}`);
      
      const response = await fetch(`${this.BASE_URL}/entries/sheet/${encodeURIComponent(sheetId)}?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to get memory entries: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Retrieved ${result.length} memory entries for sheet ${sheetId}`);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting memory entries:', error);
      throw error;
    }
  }

  /**
   * Create a conversation thread
   */
  static async createConversationThread(threadRequest: ConversationThreadRequest): Promise<ConversationThreadResponse> {
    try {
      console.log('MemoryAPI: Creating conversation thread:', threadRequest);
      
      const response = await fetch(`${this.BASE_URL}/threads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(threadRequest),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to create conversation thread: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('MemoryAPI: Conversation thread created successfully:', result.thread_id);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error creating conversation thread:', error);
      throw error;
    }
  }

  /**
   * Get a conversation thread by ID
   */
  static async getConversationThread(threadId: string): Promise<ConversationThreadResponse> {
    try {
      console.log(`MemoryAPI: Getting conversation thread ${threadId}`);
      
      const response = await fetch(`${this.BASE_URL}/threads/${encodeURIComponent(threadId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 404) {
          throw new Error(`Conversation thread not found: ${threadId}`);
        }
        throw new Error(`Failed to get conversation thread: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Retrieved conversation thread ${threadId} with ${result.messages.length} messages`);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting conversation thread:', error);
      throw error;
    }
  }

  /**
   * Add a message to a conversation thread
   */
  static async addMessageToThread(threadId: string, message: Record<string, any>): Promise<void> {
    try {
      console.log(`MemoryAPI: Adding message to thread ${threadId}:`, message);
      
      const response = await fetch(`${this.BASE_URL}/threads/${encodeURIComponent(threadId)}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 404) {
          throw new Error(`Conversation thread not found: ${threadId}`);
        }
        throw new Error(`Failed to add message to thread: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      console.log(`MemoryAPI: Message added to thread ${threadId} successfully`);
    } catch (error) {
      console.error('MemoryAPI: Error adding message to thread:', error);
      throw error;
    }
  }

  /**
   * Get parent context for a conversation thread
   */
  static async getParentContext(threadId: string, depth: number = 3): Promise<ParentContextResponse> {
    try {
      console.log(`MemoryAPI: Getting parent context for thread ${threadId}, depth ${depth}`);
      
      const response = await fetch(`${this.BASE_URL}/threads/${encodeURIComponent(threadId)}/parent-context?depth=${depth}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to get parent context: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Retrieved parent context for thread ${threadId}:`, {
        lineage_length: result.thread_lineage.length,
        parent_messages: result.parent_messages.length,
        has_summary: !!result.context_summary
      });
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting parent context:', error);
      throw error;
    }
  }

  /**
   * Generate MBCP-compatible memory blocks
   */
  static async generateMemoryBlock(blockRequest: MemoryBlockRequest): Promise<MemoryBlockResponse> {
    try {
      console.log('MemoryAPI: Generating memory block:', blockRequest);
      
      const response = await fetch(`${this.BASE_URL}/blocks/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(blockRequest),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to generate memory block: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Generated ${result.type} memory block:`, {
        content_length: result.content.length,
        metadata: result.metadata
      });
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error generating memory block:', error);
      throw error;
    }
  }

  /**
   * Check memory service health
   */
  static async checkHealth(): Promise<MemoryHealthResponse> {
    try {
      console.log('MemoryAPI: Checking memory service health');
      
      const response = await fetch(`${this.BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Memory service health check failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('MemoryAPI: Memory service health check result:', result);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error checking memory service health:', error);
      throw error;
    }
  }

  /**
   * Test connectivity to memory service
   */
  static async testConnectivity(): Promise<boolean> {
    try {
      await this.checkHealth();
      return true;
    } catch (error) {
      console.warn('MemoryAPI: Memory service connectivity test failed:', error);
      return false;
    }
  }

  /**
   * Save a snapshot to backend storage
   */
  static async saveSnapshot(snapshotRequest: SnapshotRequest): Promise<SnapshotResponse> {
    try {
      console.log('MemoryAPI: Saving snapshot:', snapshotRequest.mindbook_name);

      const response = await fetch(`${this.BASE_URL}/snapshots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(snapshotRequest),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to save snapshot: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('MemoryAPI: Snapshot saved successfully:', result.filename);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error saving snapshot:', error);
      throw error;
    }
  }

  /**
   * Get all snapshots for a mindbook
   */
  static async getSnapshots(mindbookName: string): Promise<SnapshotListResponse> {
    try {
      console.log(`MemoryAPI: Getting snapshots for mindbook: ${mindbookName}`);

      const response = await fetch(`${this.BASE_URL}/snapshots/${encodeURIComponent(mindbookName)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to get snapshots: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Retrieved ${result.total_count} snapshots for ${mindbookName}`);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting snapshots:', error);
      throw error;
    }
  }

  /**
   * Get the latest snapshot for a mindbook
   */
  static async getLatestSnapshot(mindbookName: string): Promise<any> {
    try {
      console.log(`MemoryAPI: Getting latest snapshot for mindbook: ${mindbookName}`);

      const response = await fetch(`${this.BASE_URL}/snapshots/${encodeURIComponent(mindbookName)}/latest`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 404) {
          throw new Error(`No snapshots found for mindbook: ${mindbookName}`);
        }
        throw new Error(`Failed to get latest snapshot: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`MemoryAPI: Retrieved latest snapshot for ${mindbookName}`);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting latest snapshot:', error);
      throw error;
    }
  }

  /**
   * Delete a snapshot file
   */
  static async deleteSnapshot(filename: string): Promise<void> {
    try {
      console.log(`MemoryAPI: Deleting snapshot: ${filename}`);

      const response = await fetch(`${this.BASE_URL}/snapshots/${encodeURIComponent(filename)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 404) {
          throw new Error(`Snapshot file not found: ${filename}`);
        }
        throw new Error(`Failed to delete snapshot: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      console.log(`MemoryAPI: Snapshot deleted successfully: ${filename}`);
    } catch (error) {
      console.error('MemoryAPI: Error deleting snapshot:', error);
      throw error;
    }
  }

  /**
   * Get snapshot storage statistics
   */
  static async getStorageStats(): Promise<any> {
    try {
      console.log('MemoryAPI: Getting snapshot storage statistics');

      const response = await fetch(`${this.BASE_URL}/snapshots`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to get storage stats: ${response.status} ${response.statusText} - ${errorData.detail || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('MemoryAPI: Retrieved storage statistics:', result);
      return result;
    } catch (error) {
      console.error('MemoryAPI: Error getting storage stats:', error);
      throw error;
    }
  }
}
