/**
 * DataManagementDialog
 * 
 * Provides users with transparency and control over their data storage.
 * Shows where data is stored, how much space is used, and provides export/import functionality.
 */

import React, { useState, useEffect } from 'react';
import { DataStorageManager, StorageInfo, StorageStats } from '../../core/services/DataStorageManager';
import './DataManagementDialog.css';

interface DataManagementDialogProps {
  open: boolean;
  onClose: () => void;
}

const DataManagementDialog: React.FC<DataManagementDialogProps> = ({ open, onClose }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'export'>('overview');
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);
  const [storageInfo, setStorageInfo] = useState<StorageInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // Load storage information when dialog opens
  useEffect(() => {
    if (open) {
      loadStorageData();
    }
  }, [open]);

  const loadStorageData = () => {
    setLoading(true);
    try {
      const stats = DataStorageManager.getStorageStats();
      const info = DataStorageManager.getStorageInfo();
      setStorageStats(stats);
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    try {
      const exportData = DataStorageManager.exportAllData();
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `mindback-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      alert('Data exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = e.target?.result as string;
        const result = DataStorageManager.importData(jsonData);
        
        if (result.success) {
          alert(`Successfully imported ${result.imported} items!`);
          loadStorageData(); // Refresh the display
        } else {
          alert(`Import failed. Errors: ${result.errors.join(', ')}`);
        }
      } catch (error) {
        alert('Failed to read import file. Please check the file format.');
      }
    };
    reader.readAsText(file);
  };

  const handleClearAllData = () => {
    const confirmed = window.confirm(
      'Are you sure you want to clear ALL MindBack data?\n\n' +
      'This will permanently delete:\n' +
      '• All saved MindBooks\n' +
      '• All context settings\n' +
      '• All session data\n\n' +
      'This action cannot be undone!'
    );

    if (confirmed) {
      const doubleConfirmed = window.confirm(
        'This is your final warning!\n\n' +
        'All your MindBack data will be permanently lost.\n\n' +
        'Are you absolutely sure?'
      );

      if (doubleConfirmed) {
        try {
          const result = DataStorageManager.clearAllData();
          if (result.success) {
            alert(`Successfully cleared ${result.cleared} items.`);
            loadStorageData(); // Refresh the display
          } else {
            alert(`Clear operation completed with errors: ${result.errors.join(', ')}`);
          }
        } catch (error) {
          alert('Failed to clear data. Please try again.');
        }
      }
    }
  };

  if (!open) return null;

  return (
    <div className="data-management-overlay">
      <div className="data-management-dialog">
        <div className="data-management-header">
          <h2>Data Management</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="data-management-tabs">
          <button 
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab-button ${activeTab === 'details' ? 'active' : ''}`}
            onClick={() => setActiveTab('details')}
          >
            Details
          </button>
          <button 
            className={`tab-button ${activeTab === 'export' ? 'active' : ''}`}
            onClick={() => setActiveTab('export')}
          >
            Export/Import
          </button>
        </div>

        <div className="data-management-content">
          {loading ? (
            <div className="loading">Loading storage information...</div>
          ) : (
            <>
              {activeTab === 'overview' && storageStats && (
                <div className="overview-tab">
                  <div className="storage-summary">
                    <h3>Storage Summary</h3>
                    <div className="stats-grid">
                      <div className="stat-item">
                        <span className="stat-label">Total Items:</span>
                        <span className="stat-value">{storageStats.totalKeys}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Total Size:</span>
                        <span className="stat-value">{DataStorageManager.formatBytes(storageStats.totalSize)}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">MindBooks:</span>
                        <span className="stat-value">{storageStats.mindbooks}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Context Settings:</span>
                        <span className="stat-value">{storageStats.contextSettings}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Storage Usage:</span>
                        <span className="stat-value">{storageStats.storageUsagePercent.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  <div className="storage-info">
                    <h3>Data Storage Information</h3>
                    <div className="info-section">
                      <h4>📍 Where is your data stored?</h4>
                      <p>All MindBack data is stored locally in your browser's localStorage. This means:</p>
                      <ul>
                        <li>✅ Your data stays on your device - complete privacy</li>
                        <li>✅ No server uploads - works offline</li>
                        <li>⚠️ Data is browser-specific (Chrome, Firefox, etc.)</li>
                        <li>⚠️ Data doesn't sync across devices</li>
                        <li>❌ Clearing browser data will delete your MindBooks</li>
                      </ul>
                    </div>

                    <div className="info-section">
                      <h4>💾 Backup Recommendations</h4>
                      <ul>
                        <li>Use the Export feature regularly to backup your data</li>
                        <li>Save important MindBooks as separate files</li>
                        <li>Consider using browser sync if available</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'details' && (
                <div className="details-tab">
                  <h3>Detailed Storage Breakdown</h3>
                  <div className="storage-items">
                    {storageInfo.map((item, index) => (
                      <div key={index} className={`storage-item ${item.type}`}>
                        <div className="item-header">
                          <span className="item-key">{item.key}</span>
                          <span className="item-size">{DataStorageManager.formatBytes(item.size)}</span>
                        </div>
                        <div className="item-details">
                          <span className="item-type">{item.type.replace('_', ' ')}</span>
                          <span className="item-description">{item.description}</span>
                          {item.lastModified && (
                            <span className="item-date">
                              Modified: {item.lastModified.toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'export' && (
                <div className="export-tab">
                  <div className="export-section">
                    <h3>Export Data</h3>
                    <p>Download all your MindBack data as a JSON file for backup or transfer.</p>
                    <button className="export-button" onClick={handleExportData}>
                      📥 Export All Data
                    </button>
                  </div>

                  <div className="import-section">
                    <h3>Import Data</h3>
                    <p>Restore data from a previously exported JSON file.</p>
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleImportData}
                      style={{ marginBottom: '10px' }}
                    />
                    <p className="warning">⚠️ Importing will overwrite existing data with the same keys.</p>
                  </div>

                  <div className="danger-section">
                    <h3>⚠️ Danger Zone</h3>
                    <p>Permanently delete all MindBack data from this browser.</p>
                    <button className="danger-button" onClick={handleClearAllData}>
                      🗑️ Clear All Data
                    </button>
                    <p className="warning">This action cannot be undone!</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataManagementDialog;
