/**
 * MindMapToolbar.tsx
 *
 * Toolbar component for the mind map with controls for zoom, centering, etc.
 */

import React from 'react';
import { useMindMapStore } from '../../../../core/state/MindMapStore';
import './MindMapToolbar.css';

const MindMapToolbar: React.FC = () => {
  // Use selectors to get state and actions
  const scale = useMindMapStore(state => state.scale);
  const setScale = useMindMapStore(state => state.setScale);
  const setPosition = useMindMapStore(state => state.setPosition);
  const setShowProjectDialog = useMindMapStore(state => state.setShowProjectDialog);
  const saveProject = useMindMapStore(state => state.saveProject);

  // Handle zoom in
  const handleZoomIn = () => {
    setScale(scale * 1.2);
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setScale(scale / 1.2);
  };

  // DISABLED: Competing centering system
  const handleCenter = () => {
    console.log('MindMapToolbar: 🚫 Centering DISABLED - preventing conflicts');
  };

  // Handle save
  const handleSave = () => {
    saveProject();
    // Show a toast or notification here
  };

  return (
    <div className="mindmap-toolbar">
      <div className="toolbar-group">
        <button
          className="toolbar-button"
          onClick={handleZoomIn}
          title="Zoom In"
        >
          +
        </button>
        <button
          className="toolbar-button"
          onClick={handleZoomOut}
          title="Zoom Out"
        >
          -
        </button>
        <button
          className="toolbar-button"
          onClick={handleCenter}
          title="Center View"
        >
          ⌖
        </button>
      </div>

      <div className="toolbar-group">
        <button
          className="toolbar-button"
          onClick={handleSave}
          title="Save"
        >
          💾
        </button>
        <button
          className="toolbar-button"
          onClick={() => setShowProjectDialog(true)}
          title="Project"
        >
          📁
        </button>
      </div>
    </div>
  );
};

export default MindMapToolbar;
