/**
 * ContextPanelPositioned.tsx
 *
 * A version of the ContextPanel component that uses the positioning system.
 */

import React, { useEffect, useState } from 'react';
import { useUIElement, ZIndexLayer } from '../../../core/positioning';
import ContextPanel from './ContextPanel';

interface ContextPanelPositionedProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContextPanelPositioned: React.FC<ContextPanelPositionedProps> = ({ isOpen, onClose }) => {
  // Calculate the height to leave space for the footer
  const getAdjustedHeight = () => window.innerHeight - 120; // 80px for header + 40px for footer
  // Use the positioning system
  const {
    position,
    setPosition,
    size,
    setSize,
    visible,
    setVisible,
    registerCollisionHandler,
    updateStrategy
  } = useUIElement(
    'context-panel',
    'context-panel',
    { x: 0, y: 40 },
    { width: 400, height: window.innerHeight - 120 }, // Adjusted to leave space for footer
    ZIndexLayer.PANELS,
    'left-side',
    5 // Medium priority
  );

  // Update visibility based on isOpen prop
  useEffect(() => {
    setVisible(isOpen);
  }, [isOpen, setVisible]);

  // Register collision handler
  useEffect(() => {
    registerCollisionHandler((collidingElements) => {
      // Context panel is fixed to the left side, so it doesn't need to move
      // But it can notify other elements that they need to move
      console.log('Context panel colliding with:', collidingElements);
    });
  }, [registerCollisionHandler]);

  // Apply left-side strategy when opened
  useEffect(() => {
    if (isOpen) {
      updateStrategy('left-side');
    }
  }, [isOpen, updateStrategy]);

  // Update size on window resize
  useEffect(() => {
    const handleResize = () => {
      setSize(currentSize => ({
        width: currentSize.width,
        height: getAdjustedHeight()
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setSize]); // Remove size.width from dependencies to prevent infinite loop

  if (!visible) return null;

  return (
    <div
      className={`context-panel ${isOpen ? 'open' : ''}`}
      style={{
        position: 'fixed',
        top: '40px',
        left: '0',
        width: `${size.width}px`,
        maxHeight: 'calc(100vh - 90px)', // Max height with extra margin for footer
        height: 'calc(100% - 50px)', // Ensure it doesn't reach the footer
        transform: 'none', // Override the default transform
        zIndex: 'var(--z-index-context-panel)' /* Using CSS variable for z-index */
      }}
    >
      <ContextPanel isOpen={isOpen} onClose={onClose} />
    </div>
  );
};

export default ContextPanelPositioned;
