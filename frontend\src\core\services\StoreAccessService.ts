/**
 * StoreAccessService.ts
 * 
 * Centralized service for accessing stores across the application.
 * This service provides a single point of access to all store-related functions
 * and helps avoid import timing issues.
 */

import { getMindMapStore, getActiveMindMapSheetId, useMindMapStoreRegistry } from '../state/MindMapStoreFactory';
import { useMindBookStore } from '../state/MindBookStore';
import { getPureActiveSheetId, pureStoreRegistry } from '../context/StoreRegistry';

/**
 * Store Access Service
 * Provides centralized access to all store functions
 */
export class StoreAccessService {
  /**
   * Get the active sheet ID from multiple sources
   */
  static getActiveSheetId(): string | null {
    try {
      // Try the factory first
      const factorySheetId = getActiveMindMapSheetId();
      if (factorySheetId) {
        console.log('StoreAccessService: Got sheet ID from factory:', factorySheetId);
        return factorySheetId;
      }

      // Try the pure registry
      const pureSheetId = getPureActiveSheetId();
      if (pureSheetId) {
        console.log('StoreAccessService: Got sheet ID from pure registry:', pureSheetId);
        return pureSheetId;
      }

      // Try the DOM
      const activeSheetElement = document.querySelector('.mind-sheet.active');
      const domSheetId = activeSheetElement?.getAttribute('data-sheet-id');
      if (domSheetId) {
        console.log('StoreAccessService: Got sheet ID from DOM:', domSheetId);
        return domSheetId;
      }

      // Try the MindBookStore
      const mindBookSheetId = useMindBookStore.getState().activeSheetId;
      if (mindBookSheetId) {
        console.log('StoreAccessService: Got sheet ID from MindBookStore:', mindBookSheetId);
        return mindBookSheetId;
      }

      console.warn('StoreAccessService: No active sheet ID found from any source');
      return null;
    } catch (error) {
      console.error('StoreAccessService: Error getting active sheet ID:', error);
      return null;
    }
  }

  /**
   * Get the MindMap store for a specific sheet
   */
  static getMindMapStore(sheetId: string) {
    try {
      return getMindMapStore(sheetId);
    } catch (error) {
      console.error('StoreAccessService: Error getting MindMap store:', error);
      return null;
    }
  }

  /**
   * Set the active sheet ID in all registries
   */
  static setActiveSheetId(sheetId: string | null): void {
    try {
      // Set in the Zustand registry
      useMindMapStoreRegistry.getState().setActiveSheetId(sheetId);
      console.log('StoreAccessService: Set active sheet ID in Zustand registry:', sheetId);

      // Set in the pure registry
      pureStoreRegistry.setActiveSheetId(sheetId);
      console.log('StoreAccessService: Set active sheet ID in pure registry:', sheetId);

      // Set in the MindBookStore
      if (sheetId) {
        useMindBookStore.getState().setActiveSheet(sheetId);
        console.log('StoreAccessService: Set active sheet ID in MindBookStore:', sheetId);
      }
    } catch (error) {
      console.error('StoreAccessService: Error setting active sheet ID:', error);
    }
  }

  /**
   * Get the active MindMap store (for the currently active sheet)
   */
  static getActiveMindMapStore() {
    try {
      const activeSheetId = this.getActiveSheetId();
      if (!activeSheetId) {
        console.warn('StoreAccessService: No active sheet ID available');
        return null;
      }

      return this.getMindMapStore(activeSheetId);
    } catch (error) {
      console.error('StoreAccessService: Error getting active MindMap store:', error);
      return null;
    }
  }

  /**
   * Ensure a sheet is properly activated in all registries
   */
  static activateSheet(sheetId: string): void {
    try {
      console.log('StoreAccessService: Activating sheet in all registries:', sheetId);
      
      // Get the store (this also sets it as active)
      const store = this.getMindMapStore(sheetId);
      if (store) {
        console.log('StoreAccessService: Successfully got store for sheet:', sheetId);
      }

      // Ensure it's set in all registries
      this.setActiveSheetId(sheetId);
      
      console.log('StoreAccessService: Sheet activation complete:', sheetId);
    } catch (error) {
      console.error('StoreAccessService: Error activating sheet:', error);
    }
  }
}

/**
 * Convenience functions for easier access
 */
export const getActiveSheetId = () => StoreAccessService.getActiveSheetId();
export const getActiveMindMapStore = () => StoreAccessService.getActiveMindMapStore();
export const activateSheet = (sheetId: string) => StoreAccessService.activateSheet(sheetId);
export const setActiveSheetId = (sheetId: string | null) => StoreAccessService.setActiveSheetId(sheetId);
