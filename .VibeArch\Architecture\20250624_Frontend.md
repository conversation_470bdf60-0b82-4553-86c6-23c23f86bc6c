# MindBack Frontend Architecture
*Generated: 2025-06-24*

## Overview
The MindBack frontend is a React-based application built with TypeScript and Vite, implementing a sophisticated MindBook/MindSheet architecture similar to Microsoft Excel's workbook model. The application provides AI-assisted brainstorming, mind mapping, and content generation capabilities through a governance agent system.

## Architecture Diagram

```mermaid
graph TB
    %% Entry Points
    subgraph "Entry Points"
        MAIN[main.tsx]
        INDEX[index.tsx]
        APP[AppRefactored.tsx]
    end

    %% Core Application States
    subgraph "Application States"
        STARTUP[StartupScreen]
        WORKING[AppWorkingState]
        MINDBOOK[MindBook Component]
    end

    %% Core Architecture Layers
    subgraph "Core Layer"
        subgraph "State Management"
            APPSTORE[ApplicationStore]
            MBSTORE[MindBookStore]
            MMSTORE[MindMapStore]
            CTXSTORE[ContextStore]
        end
        
        subgraph "Services"
            AUTOSNAP[AutoSnapshotService]
            MEMSNAP[MemorySnapshotService]
            KEYBOARD[KeyboardManager]
            LAYOUT[LayoutGovernanceService]
            REGMGR[RegistrationManager]
        end
        
        subgraph "Routing & Adapters"
            ROUTER[MainRouter]
            GOVADAPTER[GovernanceMindBookAdapter]
            CFADAPTER[ChatForkAdapter]
        end
        
        subgraph "Core Systems"
            MBCP[MBCP Protocol]
            POSITIONING[PositioningManager]
            GOVERNANCE[MindMapGovernance]
        end
    end

    %% Features Layer
    subgraph "Features Layer"
        subgraph "MindMap Feature"
            MMCOMP[MindMap.tsx]
            MMCANVAS[MindMapCanvas]
            MMTOOLBAR[MindMapToolbar]
            NODEBOX[NodeBox]
            NODECOMP[NodeComponent]
            CONNCOMP[ConnectionComponent]
        end
        
        subgraph "MindSheet Feature"
            MSCOMP[MindSheet.tsx]
            MSTABS[MindSheetTabs]
            MSMANAGER[SheetManager]
        end
        
        subgraph "Context Feature"
            CTXPANEL[ContextPanel]
            CTXMGR[ContextManagerDialog]
            CTXBTN[FooterContextButton]
        end
        
        subgraph "Governance Feature"
            GOVCHAT[GovernanceChatDialog]
            GOVBOX[GovernanceBoxPositioned]
            GOVBTN[FooterGovernanceButton]
        end
    end

    %% UI Components Layer
    subgraph "UI Components"
        subgraph "Shared Components"
            ERRORBND[ErrorBoundary]
            HAMBURGER[HamburgerMenu]
            DIALOGS[Dialog Components]
        end
        
        subgraph "Layout Components"
            HEADER[Header]
            FOOTER[Footer]
            SIDEBAR[Sidebar]
        end
    end

    %% Services Layer
    subgraph "Services Layer"
        subgraph "API Services"
            GOVLLM[GovernanceLLM API]
            MEMAPI[MemoryAPI]
            MINDMAPAPI[MindMapAPI]
        end
        
        subgraph "Integration Services"
            CHATMEM[ChatMemoryService]
            MEMCONS[MemoryConsolidationService]
            BLOCKCHAIN[BlockchainSnapshotService]
        end
    end

    %% External Systems
    subgraph "External Systems"
        BACKEND[FastAPI Backend]
        LLMPROVIDERS[LLM Providers<br/>GPT-4, Claude, Gemini]
        STORAGE[Local Storage]
    end

    %% Data Flow Connections
    MAIN --> APP
    INDEX --> APP
    APP --> STARTUP
    APP --> WORKING
    WORKING --> MINDBOOK

    %% Core State Management
    MINDBOOK --> MBSTORE
    MINDBOOK --> MMSTORE
    MINDBOOK --> APPSTORE
    
    %% Feature Integration
    MINDBOOK --> MMCOMP
    MINDBOOK --> MSCOMP
    MINDBOOK --> GOVCHAT
    MINDBOOK --> CTXPANEL

    %% MindMap Feature Flow
    MMCOMP --> MMCANVAS
    MMCOMP --> MMTOOLBAR
    MMCANVAS --> NODECOMP
    MMCANVAS --> CONNCOMP
    MMCANVAS --> NODEBOX

    %% MindSheet Feature Flow
    MSCOMP --> MSTABS
    MSCOMP --> MSMANAGER

    %% Governance Flow
    GOVCHAT --> GOVBOX
    GOVBOX --> ROUTER
    ROUTER --> GOVADAPTER
    GOVADAPTER --> MBSTORE

    %% Services Integration
    AUTOSNAP --> MEMSNAP
    MEMSNAP --> MEMAPI
    GOVLLM --> BACKEND
    MEMAPI --> BACKEND
    MINDMAPAPI --> BACKEND

    %% Context System
    CTXPANEL --> CTXSTORE
    CTXMGR --> CTXSTORE

    %% External Connections
    BACKEND --> LLMPROVIDERS
    AUTOSNAP --> STORAGE
    MBSTORE --> STORAGE

    %% Styling
    classDef entryPoint fill:#e1f5fe
    classDef coreLayer fill:#f3e5f5
    classDef featureLayer fill:#e8f5e8
    classDef serviceLayer fill:#fff3e0
    classDef external fill:#ffebee

    class MAIN,INDEX,APP entryPoint
    class APPSTORE,MBSTORE,MMSTORE,AUTOSNAP,ROUTER,MBCP coreLayer
    class MMCOMP,MSCOMP,GOVCHAT,CTXPANEL featureLayer
    class GOVLLM,MEMAPI,CHATMEM serviceLayer
    class BACKEND,LLMPROVIDERS,STORAGE external
```

## Key Architectural Patterns

### 1. MindBook/MindSheet Architecture
- **MindBook**: Top-level container (similar to Excel Workbook)
- **MindSheet**: Individual sheets within a MindBook (similar to Excel Worksheets)
- **Content Types**: MindMap, ChatFork, and other specialized content types

### 2. State Management Hierarchy
```
ApplicationStore (Global App State)
├── MindBookStore (Workbook-level State)
│   ├── MindMapStore (Sheet-specific State)
│   └── ContextStore (Context Settings)
└── UI State (Panels, Dialogs, etc.)
```

### 3. Feature-Based Organization
Each major feature (MindMap, MindSheet, Context, Governance) is organized as a self-contained module with:
- Components (UI)
- Services (Business Logic)
- Store (State Management)
- Types (TypeScript Definitions)

### 4. Service Layer Pattern
- **API Services**: Handle backend communication
- **Integration Services**: Manage complex workflows
- **Core Services**: Provide system-wide functionality

## Component Responsibilities

### Core Components
- **AppRefactored.tsx**: Main application orchestrator
- **AppWorkingState.tsx**: Working state UI coordinator
- **MindBook**: Primary workspace container

### Feature Components
- **MindMap**: Interactive mind mapping interface
- **MindSheet**: Sheet-based content management
- **GovernanceChat**: AI interaction interface
- **ContextPanel**: Context settings management

### Service Components
- **AutoSnapshotService**: Automatic state persistence
- **MainRouter**: Intent-based routing system
- **RegistrationManager**: Event tracking and logging

## Data Flow Patterns

### 1. User Interaction Flow
```
User Input → Governance Box → MainRouter → Feature Component → State Store → Backend API
```

### 2. AI Response Flow
```
Backend API → GovernanceLLM → MainRouter → MindBook → MindSheet → UI Update
```

### 3. Snapshot Flow
```
User Action → AutoSnapshotService → MemorySnapshotService → MemoryAPI → Backend Storage
```

## Technology Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **State Management**: Zustand
- **UI Library**: Material-UI (MUI)
- **Canvas Rendering**: Konva.js (for mind maps)
- **Routing**: React Router
- **Styling**: CSS Modules + Global CSS

## Integration Points
- **Backend API**: FastAPI-based REST and WebSocket endpoints
- **LLM Providers**: GPT-4, Claude Sonnet, Gemini integration
- **Storage**: Local storage for persistence and caching
- **Memory System**: MBCP-based memory and snapshot management
