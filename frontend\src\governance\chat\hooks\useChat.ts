import { useState } from 'react';
import useChatStore, { Message } from '../state/ChatStore';
import RegistrationManager, { EventType } from '../../../core/services/RegistrationManager';
import { useMindBookStore } from '../../../core/state/MindBookStore';
import { MindSheetContentType } from '../../../features/mindsheet/MindSheet';
import { activateSheet } from '../../../core/services/StoreAccessService';
import { initializeMindMapSheet } from '../../../core/services/MindSheetService';
import { ChatMemoryService } from '../../../services/ChatMemoryService';
import { v4 as uuidv4 } from 'uuid';

interface UseChatOptions {
  selectedModel?: string;
  useLiveLLM?: boolean;
}

/**
 * Creates a mindmap from the MBCP response data
 * This function creates a new mindsheet and initializes a mindmap within it
 * It uses the sheet-specific store system via MindSheetService
 *
 * @param mbcpData The MBCP data from the LLM response
 * @returns True if the mindmap was created successfully, false otherwise
 */
const createMindmapFromResponse = (mbcpData: any): boolean => {
  try {
    console.log('Creating mindmap from MBCP data:', mbcpData);

    // Get the MindBookStore
    const mindBookStore = useMindBookStore.getState();

    // Extract title and description from MBCP data
    const title = mbcpData.text || 'New Mindmap';
    const description = mbcpData.description || 'Click to edit this mindmap';

    // Check if we need to create a default mindmap structure
    let processedData = mbcpData;

    // If there's no mindmap structure, create a default one
    if (!mbcpData.mindmap) {
      console.log('No mindmap structure found, creating default structure');

      // Create a default mindmap structure with the title as the root node
      processedData = {
        ...mbcpData,
        mindmap: {
          root: {
            id: uuidv4(),
            text: title,
            description: description,
            metadata: {
              intent: 'teleological',
              tags: ['mindmap']
            },
            children: [
              {
                id: uuidv4(),
                text: '1. Main Topic',
                description: 'Enter your main topic here',
                metadata: {
                  intent: 'teleological',
                  tags: ['main-topic']
                },
                children: []
              }
            ]
          }
        }
      };

      console.log('Created default mindmap structure:', processedData.mindmap);
    }

    // Create a new mindsheet with the processed MBCP data
    const sheetId = mindBookStore.createMindMapSheet(title, processedData);
    console.log('Created mindsheet with ID:', sheetId);

    if (!sheetId) {
      console.error('Failed to create mindsheet');
      return false;
    }

    // Register the mindsheet creation event
    RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, { id: sheetId });

    // Set this sheet as active in all registries
    mindBookStore.setActiveSheet(sheetId);
    activateSheet(sheetId);
    console.log('Set sheet as active:', sheetId);

    // Initialize the mindmap in the sheet using the MindSheetService
    // This will use the sheet-specific store system
    const success = initializeMindMapSheet(sheetId, processedData);
    console.log('Initialized mindmap in sheet:', sheetId, 'success:', success);

    if (success) {
      // Dispatch an event to notify components that the mindmap is ready
      const event = new CustomEvent('mindback:mindmap_initialized', {
        detail: {
          sheetId: sheetId,
          hasData: true,
          shouldCenterView: true
        }
      });
      document.dispatchEvent(event);
      console.log('Dispatched mindmap_initialized event for sheet:', sheetId);
    }

    return success;
  } catch (error) {
    console.error('Error creating mindmap from MBCP data:', error);
    return false;
  }
};

export const useChat = (options: UseChatOptions = {}, onActionButtonClick?: (action: any) => void) => {
  const { selectedModel = 'gpt-4o-mini', useLiveLLM = true } = options;

  // Use the global chat store instead of local state
  const {
    messages,
    addMessage,
    setMessages,
    isSubmitting,
    setIsSubmitting
  } = useChatStore();

  const handleMessageSubmit = async (text: string) => {
    if (!text.trim()) return;

    // Initialize memory service if not already done
    const memoryService = ChatMemoryService.getInstance();
    if (!memoryService.isBackendEnabled()) {
      await memoryService.initializeBackend().catch(error => {
        console.warn('useChat: Failed to initialize memory backend, continuing without:', error);
      });
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text,
      sender: 'user',
      timestamp: new Date()
    };

    // Store user message in memory service
    memoryService.addStructuredMessage(
      text,
      'user',
      undefined, // responseType
      undefined, // suggestedActions
      undefined, // parentId
      ['governance-chat'], // tags
      'sent' // status
    );

    // Register user input event
    RegistrationManager.registerEvent(EventType.USER_INPUT, { text });

    addMessage(userMessage);
    setIsSubmitting(true);

    try {
      // If not using live LLM, just return a mock response
      if (!useLiveLLM) {
        setTimeout(() => {
          const mockMessage: Message = {
            id: Date.now().toString(),
            text: `[Mock Response] I received your message: "${text}"`,
            sender: 'assistant',
            timestamp: new Date()
          };

          setMessages(prev => [...prev, mockMessage]);
          setIsSubmitting(false);
        }, 500);
        return;
      }

      // Register LLM request event
      RegistrationManager.registerEvent(EventType.LLM_REQUEST_SENT, {
        model: selectedModel,
        prompt: text
      });

      // Register API request event
      RegistrationManager.registerEvent(EventType.API_REQUEST_SENT, {
        endpoint: '/api/llm/chat',
        model: selectedModel
      });

      // Prepare memory context for enhanced LLM understanding
      let memoryContext = '';
      try {
        const ctxBlock = await memoryService.generateEnhancedMemoryBlock('CTX');
        const memBlock = await memoryService.generateEnhancedMemoryBlock('MEM');

        memoryContext = `\n\n[CTX]::${ctxBlock.content}\n[MEM]::${memBlock.content}`;

        console.log('useChat: Including memory context:', {
          ctx_length: ctxBlock.content.length,
          mem_length: memBlock.content.length,
          source: ctxBlock.metadata.source
        });
      } catch (memoryError) {
        console.warn('useChat: Failed to generate memory context, proceeding without:', memoryError);
        memoryContext = '';
      }

      // Combine user message with memory context
      const enhancedPrompt = text + memoryContext;

      // CONTINUATION WORKFLOW DETECTION
      // Check if mindsheets exist to determine workflow type
      const mindBookStore = useMindBookStore.getState();
      const currentMindBook = mindBookStore.mindBook;
      const hasExistingSheets = currentMindBook?.sheets && currentMindBook.sheets.length > 0;

      console.log('🔄 Workflow Detection:', {
        hasExistingSheets,
        sheetCount: currentMindBook?.sheets?.length || 0,
        mindBookName: currentMindBook?.name || 'Unknown'
      });

      let response;

      if (hasExistingSheets) {
        // CONTINUATION WORKFLOW: Use continuation API with snapshot data
        console.log('🔄 === CONTINUATION WORKFLOW DETECTED ===');
        console.log('Existing sheets found, using continuation API endpoint');

        // Trigger snapshot creation first
        try {
          console.log('📸 Triggering snapshot before continuation workflow...');
          if (window.triggerSnapshot) {
            await window.triggerSnapshot('continuation-workflow');
            console.log('✅ Snapshot created for continuation workflow');
          } else {
            console.warn('⚠️ AutoSnapshot service not available, proceeding without snapshot');
          }
        } catch (snapshotError) {
          console.warn('⚠️ Failed to create snapshot, proceeding with continuation workflow:', snapshotError);
        }

        // Collect current mindbook state as snapshot data
        const snapshotData = {
          mindBook: currentMindBook,
          timestamp: new Date().toISOString(),
          metadata: {
            workflow_type: 'continuation',
            trigger: 'governance_input',
            user_prompt: enhancedPrompt
          }
        };

        // Get active sheet ID if available
        const activeSheetId = mindBookStore.activeSheetId ||
                             (currentMindBook?.sheets && currentMindBook.sheets.length > 0 ?
                              currentMindBook.sheets[0].id : null);

        console.log('📋 Continuation request data:', {
          mindbook_name: currentMindBook?.name || 'Untitled',
          active_sheet_id: activeSheetId,
          snapshot_size: JSON.stringify(snapshotData).length
        });

        // Call continuation API endpoint
        response = await fetch('http://localhost:8000/api/llm/continuation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: enhancedPrompt,
            mindbook_name: currentMindBook?.name || 'Untitled',
            snapshot_data: snapshotData,
            model: selectedModel,
            temperature: 0.7,
            context_level: 'operational', // TODO: Get from context settings
            active_sheet_id: activeSheetId,
            workflow_type: 'continuation'
          }),
        });

        console.log('🔄 Continuation API response status:', response.status);

      } else {
        // INITIATION WORKFLOW: Use standard chat API
        console.log('🚀 === INITIATION WORKFLOW DETECTED ===');
        console.log('No existing sheets found, using initiation API endpoint');

        response = await fetch('http://localhost:8000/api/llm/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: enhancedPrompt,
            model: selectedModel, // Use the selected model
            memory_enabled: true,
            memory_context_length: memoryContext.length
            // No prompt_type - let backend use default intent detection
            // This will populate the {g-llm_dialogue} placeholder in the prompt template
          }),
        });

        console.log('🚀 Initiation API response status:', response.status);
      }

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      // Register API response event
      RegistrationManager.registerEvent(EventType.API_RESPONSE_RECEIVED, {
        endpoint: '/api/llm/chat',
        status: response.status
      });

      const data = await response.json();
      console.log('API response:', data);
      console.log('Intent from API:', data.content?.intent);

      // Register LLM response event
      RegistrationManager.registerEvent(EventType.LLM_RESPONSE_RECEIVED, {
        model: selectedModel
      });

      // Register intent detection event
      if (data.content?.intent) {
        RegistrationManager.registerEvent(EventType.INTENT_DETECTED, {
          intent: data.content.intent
        });
      }

      // Check if the response has intent information but no suggested actions
      let suggestedActions = data.content?.metadata?.suggestedActions || data.suggestedActions || [];

      // The backend should provide the suggestedActions with the MBCP data
      // We should not need to generate them in the frontend
      if (suggestedActions.length === 0) {
        console.log('No suggested actions provided by backend, this may indicate an issue with the backend response');
      } else {
        console.log('Using suggested actions from backend:', suggestedActions);
      }

      // If there's teleological intent, check for mindmap data
      // Check both data.content.intent and data.intent for teleological intent
      const isTeleological = data.content?.intent === 'teleological' || data.intent === 'teleological';

      if (isTeleological) {
        console.log('Detected teleological intent');

        // For teleological intent detected during conversation, add a button
        // but don't automatically create the mindmap
        console.log('Teleological intent detected - adding Build Mind Map button');

        // Prepare the MBCP data for the action
        const mbcpData = data.content?.mindmap ? data.content :
                         data.mindmap ? {
                           text: data.text || 'Mindmap',
                           description: data.description || '',
                           intent: data.intent || 'teleological',
                           mindmap: data.mindmap
                         } :
                         data.content || {
                           text: data.text || 'Mindmap',
                           description: data.description || '',
                           intent: data.intent || 'teleological'
                         };

        // Add a create_mindmap action to allow the user to trigger it manually
        suggestedActions.push({
          type: 'create_mindmap',
          label: 'Build Mind Map',
          data: {
            topic: mbcpData.text || 'Mindmap',
            description: mbcpData.description || '',
            template_type: 'teleological',
            mbcpData: mbcpData
          }
        });
        console.log('Added create_mindmap action for manual triggering');
      }

      // If there's exploratory intent but no actions, generate them
      if (data.content?.intent === 'exploratory' && suggestedActions.length === 0) {
        // Extract topic name for the button label
        const topicName = data.content.text || 'Topic';

        // Add a show_chatfork action for exploratory responses
        suggestedActions = [{
          type: 'show_chatfork',
          label: `Explore ${topicName}`,
          data: {
            title: data.content.text || 'Exploration',
            description: data.content.description || '',
            intent: 'exploratory',
            responseType: {
              type: 'exploratory',
              requiresMindmap: false,
              requiresChatFork: true
            },
            content: data.content, // Store the complete MBCP data
            templateOutput: data.content // For compatibility with existing code
          }
        }];
        console.log('Generated suggested actions for exploratory intent:', suggestedActions);
      }

      // Create assistant message from response
      // Use the intent provided by the LLM through the MBCP protocol
      // Check both data.content.intent and data.intent for the intent value
      const intent = data.content?.intent || data.intent || 'miscellaneous';

      // For factual intents, prioritize the description field (which contains the actual answer)
      // For other intents, use the text field or fall back to description
      let messageText;
      if (intent === 'factual' && data.content?.description) {
        console.log('FACTUAL INTENT: Using description field for display:', data.content.description);
        messageText = data.content.description;
      } else if (data.content?.text) {
        messageText = data.content.text;
      } else if (data.content?.description) {
        messageText = data.content.description;
      } else if (data.text) {
        messageText = data.text;
      } else if (data.description) {
        messageText = data.description;
      } else {
        messageText = `I received your message: "${text}"`;
      }

      console.log('Creating message with intent from LLM:', intent);
      console.log('Available fields:', {
        'content.text': data.content?.text,
        'content.description': data.content?.description,
        'data.text': data.text,
        'data.description': data.description
      });

      // Create a properly structured MBCP data object that combines data from both possible locations
      const mbcpData = data.content ? { ...data.content } : {
        text: data.text || messageText,
        description: data.description || messageText,
        intent: data.intent || intent
      };

      // If we have mindmap data at the top level but not in content, add it to our mbcpData
      if (data.mindmap && !mbcpData.mindmap) {
        mbcpData.mindmap = data.mindmap;
      }

      const assistantMessage: Message = {
        id: Date.now().toString(),
        text: messageText,
        sender: 'assistant',
        timestamp: new Date(),
        responseType: {
          type: intent,
          requiresMindmap: intent === 'teleological',
          mbcpData: mbcpData // Store the full MBCP data for later use
        },
        suggestedActions: suggestedActions
      };

      console.log('Created assistant message:', assistantMessage);
      console.log('Response type:', assistantMessage.responseType);

      // Store LLM response in memory service
      memoryService.addStructuredMessage(
        messageText,
        'llm',
        assistantMessage.responseType, // responseType
        assistantMessage.suggestedActions, // suggestedActions
        userMessage.id, // parentId - link to user message
        ['governance-chat', intent], // tags
        'received' // status
      );

      addMessage(assistantMessage);
    } catch (error) {
      console.error('Error sending message to API:', error);

      // Add error message if API fails
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: `Error: Could not get a response from the AI. Please try again later.`,
        sender: 'assistant',
        timestamp: new Date()
      };

      addMessage(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAction = (action: any) => {
    console.log('Action clicked:', action);

    // Register user action event
    RegistrationManager.registerEvent(EventType.USER_ACTION, {
      action: action.type,
      label: action.label
    });

    // For create_mindmap actions, add more detailed logging
    if (action.type === 'create_mindmap') {
      console.log('Processing create_mindmap action:', action);
      console.log('Action data:', action.data);

      // Make sure we have the original MBCP data attached
      if (action.data && !action.data.mbcpData && action.data.template_type === 'teleological') {
        // Get the last assistant message with teleological intent
        const lastTeleologicalMessage = [...messages].reverse().find(
          msg => msg.sender === 'assistant' && msg.responseType?.type === 'teleological'
        );

        if (lastTeleologicalMessage) {
          console.log('Attaching MBCP data from last teleological message');
          action.data.mbcpData = lastTeleologicalMessage.responseType?.mbcpData;
        }
      }
    }

    // For show_chatfork actions, ensure all required data is attached
    if (action.type === 'show_chatfork') {
      console.log('Processing show_chatfork action:', action);

      // Make sure we have the complete data for exploratory content
      if (action.data && action.data.intent === 'exploratory') {
        // Get the last assistant message with exploratory intent
        const lastExploratoryMessage = [...messages].reverse().find(
          msg => msg.sender === 'assistant' && msg.responseType?.type === 'exploratory'
        );

        if (lastExploratoryMessage) {
          console.log('Ensuring complete data for exploratory message');

          // Make sure all necessary fields are populated
          if (!action.data.responseType) {
            action.data.responseType = {
              type: 'exploratory',
              requiresMindmap: false,
              requiresChatFork: true
            };
          }

          // Make sure we have the original MBCP data
          if (!action.data.content) {
            action.data.content = lastExploratoryMessage.responseType?.mbcpData;
          }

          // For compatibility with different parsing approaches
          if (!action.data.templateOutput) {
            action.data.templateOutput = lastExploratoryMessage.responseType?.mbcpData;
          }
        }
      }

      console.log('Enhanced show_chatfork action:', action);
    }

    // Pass the action to the parent component via onActionButtonClick
    if (onActionButtonClick) {
      console.log('Forwarding action to parent component:', action);
      onActionButtonClick(action);
    } else {
      console.error('No onActionButtonClick handler provided to useChat hook');
    }
  };

  // Global lock for mindmap building to prevent infinite loops
  let isMindmapBuildingLocked = false;

  const handleBuildMindmap = () => {
    console.log('Build mindmap clicked');

    // Prevent multiple simultaneous mindmap creations using a global lock
    if (isMindmapBuildingLocked) {
      console.warn('Already building a mindmap, ignoring new request');
      return;
    }

    // Set global lock
    isMindmapBuildingLocked = true;

    try {
      // Register mindmap selected event
      RegistrationManager.registerEvent(EventType.MINDMAP_SELECTED, {
        action: 'build_mindmap'
      });

      // Get the last assistant message that has the MBCP data
      const lastAssistantMessage = [...messages].reverse().find(
        msg => msg.sender === 'assistant' &&
               msg.responseType?.type === 'teleological' &&
               !msg.mindmapCreated // Add check for mindmapCreated flag
      );

      if (!lastAssistantMessage) {
        console.error('No teleological message found to build mindmap from or mindmap already created');
        isMindmapBuildingLocked = false;
        return;
      }

      console.log('Building mindmap from message:', lastAssistantMessage);

      // IMPORTANT: Mark the message as having a mindmap created BEFORE creating the mindmap
      // This prevents the button from being clicked multiple times
      const updatedMessages = messages.map(msg => {
        if (msg.id === lastAssistantMessage.id) {
          return { ...msg, mindmapCreated: true };
        }
        return msg;
      });

      // Update the messages in the store FIRST
      useChatStore.getState().setMessages(updatedMessages);

    // Add a system message indicating we're creating a mindmap
    const startMessage = {
      id: Date.now().toString(),
      text: `Creating mindmap from "${lastAssistantMessage.text || 'teleological content'}"...`,
      sender: 'system',
      timestamp: new Date()
    };
    useChatStore.getState().addMessage(startMessage);

    // Get the MBCP data from the message
    let mbcpData = lastAssistantMessage.responseType?.mbcpData;

    // Log detailed information about the available data
    console.log('Available MBCP data in message:', mbcpData);

    // If we don't have MBCP data, create a basic structure
    if (!mbcpData) {
      console.log('No MBCP data found in message, creating basic structure');
      mbcpData = {
        text: lastAssistantMessage.text || 'Mindmap',
        description: lastAssistantMessage.text || '',
        intent: 'teleological'
      };
    }

    // If we don't have mindmap data, create a basic structure
    if (!mbcpData.mindmap) {
      console.log('No mindmap structure found in MBCP data, creating a basic structure');

      // Create a basic mindmap structure with the title as the root node
      const basicMindmap = {
        root: {
          id: uuidv4(),
          text: mbcpData.text || 'Mindmap',
          description: mbcpData.description || 'Click to edit',
          metadata: {
            intent: 'teleological',
            tags: ['mindmap']
          },
          children: [
            {
              id: uuidv4(),
              text: '1. Main Topic',
              description: 'Enter your main topic here',
              metadata: {
                intent: 'teleological',
                tags: ['main-topic']
              },
              children: []
            },
            {
              id: uuidv4(),
              text: '2. Secondary Topic',
              description: 'Enter your secondary topic here',
              metadata: {
                intent: 'teleological',
                tags: ['secondary-topic']
              },
              children: []
            },
            {
              id: uuidv4(),
              text: '3. Additional Topic',
              description: 'Enter an additional topic here',
              metadata: {
                intent: 'teleological',
                tags: ['additional-topic']
              },
              children: []
            }
          ]
        }
      };

      // Add the mindmap structure to the MBCP data
      mbcpData.mindmap = basicMindmap;
      console.log('Created basic mindmap structure with multiple nodes:', mbcpData.mindmap);
    }

    // Create the mindmap directly using the createMindmapFromResponse function
    console.log('Creating mindmap directly with MBCP data:', mbcpData);
    const success = createMindmapFromResponse(mbcpData);

    if (success) {
      console.log('Successfully created mindmap');

      // We already marked the message as having a mindmap created before starting
      // No need to update the messages again

      // Add a system message indicating the mindmap was created
      const systemMessage = {
        id: Date.now().toString(),
        text: `Mindmap created successfully from "${mbcpData.text || 'teleological content'}"`,
        sender: 'system',
        timestamp: new Date()
      };
      useChatStore.getState().addMessage(systemMessage);

      // SUCCESS: Do NOT call onActionButtonClick to prevent duplicate mindmap creation
      // The mindmap was already created successfully above
    } else {
      console.error('Failed to create mindmap');

      // Try the old approach as a fallback
      console.log('Trying fallback approach with action handler');

      // Find the create_mindmap action in the message's suggested actions
      let mindmapAction = lastAssistantMessage.suggestedActions?.find(
        action => action.type === 'create_mindmap'
      );

      if (!mindmapAction) {
        mindmapAction = {
          type: 'create_mindmap',
          label: 'Build Mindmap (Limited)',
          data: {
            topic: mbcpData.text || 'Mindmap',
            description: mbcpData.description || '',
            template_type: 'teleological',
            mbcpData: mbcpData
          }
        };
      }

      // We already marked the message as having a mindmap created before starting
      // No need to update the messages again in the fallback case

      // Add a system message indicating the mindmap was created via fallback
      const systemMessage = {
        id: Date.now().toString(),
        text: `Mindmap created via fallback method from "${mbcpData.text || 'teleological content'}"`,
        sender: 'system',
        timestamp: new Date()
      };
      useChatStore.getState().addMessage(systemMessage);

      // Dispatch the action through the handleAction function
      handleAction(mindmapAction);
    }
    } catch (error) {
      console.error('Error in handleBuildMindmap:', error);

      // Add an error message
      const errorMessage = {
        id: Date.now().toString(),
        text: `Error creating mindmap: ${error.message || 'Unknown error'}`,
        sender: 'system',
        timestamp: new Date()
      };
      useChatStore.getState().addMessage(errorMessage);
    } finally {
      // Use a timeout to release the lock after a short delay
      // This helps prevent rapid successive calls
      setTimeout(() => {
        // Always reset the building flag
        isMindmapBuildingLocked = false;
        console.log('Mindmap building lock released');
      }, 500);
    }
  };

  return {
    messages,
    isSubmitting,
    handleMessageSubmit,
    handleAction,
    handleBuildMindmap
  };
};