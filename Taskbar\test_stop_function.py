#!/usr/bin/env python3
"""
Test script to verify the stop_servers functionality
"""

import psutil
import sys
import os
from pathlib import Path

def find_mindback_processes():
    """Find all processes that look like MindBack servers"""
    mindback_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            proc_name = proc_info['name'].lower()
            cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
            
            # Check for server processes
            is_backend = False
            is_frontend = False
            
            # Backend patterns
            if proc_name in ['python.exe', 'python', 'uvicorn.exe', 'uvicorn']:
                if any(keyword in cmdline.lower() for keyword in ['uvicorn', 'main:app', '8000', 'mindback']):
                    is_backend = True
            
            # Frontend patterns  
            elif proc_name in ['node.exe', 'node']:
                if any(keyword in cmdline.lower() for keyword in ['vite', 'dev', '5173']):
                    is_frontend = True
            
            if is_backend or is_frontend:
                server_type = "Backend" if is_backend else "Frontend"
                mindback_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_name,
                    'type': server_type,
                    'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return mindback_processes

def main():
    print("MindBack Process Scanner")
    print("=" * 50)
    
    processes = find_mindback_processes()
    
    if not processes:
        print("✅ No MindBack server processes found")
        return
    
    print(f"Found {len(processes)} MindBack server process(es):")
    print()
    
    for proc in processes:
        print(f"🔍 {proc['type']} Server:")
        print(f"   PID: {proc['pid']}")
        print(f"   Name: {proc['name']}")
        print(f"   Command: {proc['cmdline']}")
        print()
    
    print("=" * 50)
    print("💡 If you see processes above, the stop function should target them.")
    print("💡 Run the launcher and test the 'Stop Servers' button.")

if __name__ == "__main__":
    main()
