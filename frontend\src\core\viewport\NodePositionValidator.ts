/**
 * NodePositionValidator.ts
 * 
 * Utility functions for validating and adjusting node positions
 * in MindBack mindmap functionality. Works with ViewportManager
 * to ensure optimal node placement.
 * 
 * Created: 2025-06-24
 * Part of: Phase 1 - Node Positioning System Overhaul
 */

import { getViewportManager, NodePosition, NodeDimensions } from './ViewportManager';
import { Node } from '../types/MindMapTypes';

export interface ValidationResult {
  isValid: boolean;
  adjustedPosition?: NodePosition;
  issues: string[];
  adjustmentReason?: string;
}

export interface LayoutValidationOptions {
  preventOverlap: boolean;
  ensureVisibility: boolean;
  maintainHierarchy: boolean;
  minNodeSpacing: number;
}

/**
 * NodePositionValidator provides utilities for validating and adjusting
 * node positions to ensure optimal mindmap layout and visibility.
 */
export class NodePositionValidator {
  private viewportManager = getViewportManager();
  
  /**
   * Validate a single node position
   */
  public validateNodePosition(
    position: NodePosition,
    dimensions?: NodeDimensions,
    options: Partial<LayoutValidationOptions> = {}
  ): ValidationResult {
    const issues: string[] = [];
    const opts = {
      preventOverlap: true,
      ensureVisibility: true,
      maintainHierarchy: false,
      minNodeSpacing: 50,
      ...options
    };

    // Check viewport visibility
    const isVisible = this.viewportManager.isNodeVisible(position, dimensions);
    if (!isVisible && opts.ensureVisibility) {
      issues.push('Node position is outside viewport boundaries');
    }

    // If there are issues, calculate adjusted position
    let adjustedPosition: NodePosition | undefined;
    let adjustmentReason: string | undefined;

    if (issues.length > 0) {
      if (!isVisible && opts.ensureVisibility) {
        adjustedPosition = this.viewportManager.ensureNodeVisible(position, dimensions);
        adjustmentReason = 'Moved node to ensure viewport visibility';
      }
    }

    return {
      isValid: issues.length === 0,
      adjustedPosition,
      issues,
      adjustmentReason
    };
  }

  /**
   * Validate multiple node positions and resolve conflicts
   */
  public validateNodeLayout(
    nodes: Record<string, Node>,
    options: Partial<LayoutValidationOptions> = {}
  ): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};
    const opts = {
      preventOverlap: true,
      ensureVisibility: true,
      maintainHierarchy: true,
      minNodeSpacing: 100,
      ...options
    };

    // First pass: validate individual positions
    const nodePositions: Record<string, NodePosition> = {};
    for (const [nodeId, node] of Object.entries(nodes)) {
      const position = { x: node.x, y: node.y };
      const dimensions = { width: node.width || 200, height: node.height || 100 };
      
      nodePositions[nodeId] = position;
      results[nodeId] = this.validateNodePosition(position, dimensions, opts);
    }

    // Second pass: check for overlaps if enabled
    if (opts.preventOverlap) {
      this.resolveNodeOverlaps(nodes, results, opts.minNodeSpacing);
    }

    return results;
  }

  /**
   * Resolve overlapping nodes by adjusting their positions
   */
  private resolveNodeOverlaps(
    nodes: Record<string, Node>,
    results: Record<string, ValidationResult>,
    minSpacing: number
  ): void {
    const nodeIds = Object.keys(nodes);
    
    for (let i = 0; i < nodeIds.length; i++) {
      for (let j = i + 1; j < nodeIds.length; j++) {
        const nodeIdA = nodeIds[i];
        const nodeIdB = nodeIds[j];
        const nodeA = nodes[nodeIdA];
        const nodeB = nodes[nodeIdB];

        // Get current positions (use adjusted if available)
        const posA = results[nodeIdA].adjustedPosition || { x: nodeA.x, y: nodeA.y };
        const posB = results[nodeIdB].adjustedPosition || { x: nodeB.x, y: nodeB.y };

        // Check for overlap
        const distance = this.calculateDistance(posA, posB);
        const requiredDistance = minSpacing + (nodeA.width || 200) / 2 + (nodeB.width || 200) / 2;

        if (distance < requiredDistance) {
          // Nodes are overlapping, adjust positions
          const adjustedPositions = this.separateOverlappingNodes(
            posA, posB, nodeA, nodeB, requiredDistance
          );

          // Update results with adjusted positions
          results[nodeIdA] = {
            ...results[nodeIdA],
            isValid: false,
            adjustedPosition: adjustedPositions.nodeA,
            issues: [...results[nodeIdA].issues, 'Node overlaps with another node'],
            adjustmentReason: 'Separated overlapping nodes'
          };

          results[nodeIdB] = {
            ...results[nodeIdB],
            isValid: false,
            adjustedPosition: adjustedPositions.nodeB,
            issues: [...results[nodeIdB].issues, 'Node overlaps with another node'],
            adjustmentReason: 'Separated overlapping nodes'
          };
        }
      }
    }
  }

  /**
   * Calculate distance between two positions
   */
  private calculateDistance(posA: NodePosition, posB: NodePosition): number {
    return Math.sqrt(Math.pow(posA.x - posB.x, 2) + Math.pow(posA.y - posB.y, 2));
  }

  /**
   * Separate two overlapping nodes
   */
  private separateOverlappingNodes(
    posA: NodePosition,
    posB: NodePosition,
    nodeA: Node,
    nodeB: Node,
    requiredDistance: number
  ): { nodeA: NodePosition; nodeB: NodePosition } {
    // Calculate the angle between the nodes
    const angle = Math.atan2(posB.y - posA.y, posB.x - posA.x);
    
    // Calculate how much to move each node
    const currentDistance = this.calculateDistance(posA, posB);
    const moveDistance = (requiredDistance - currentDistance) / 2;

    // Move nodes apart along the line connecting them
    const adjustedA: NodePosition = {
      x: posA.x - Math.cos(angle) * moveDistance,
      y: posA.y - Math.sin(angle) * moveDistance
    };

    const adjustedB: NodePosition = {
      x: posB.x + Math.cos(angle) * moveDistance,
      y: posB.y + Math.sin(angle) * moveDistance
    };

    // Ensure both adjusted positions are still within viewport
    return {
      nodeA: this.viewportManager.ensureNodeVisible(adjustedA, {
        width: nodeA.width || 200,
        height: nodeA.height || 100
      }),
      nodeB: this.viewportManager.ensureNodeVisible(adjustedB, {
        width: nodeB.width || 200,
        height: nodeB.height || 100
      })
    };
  }

  /**
   * Generate optimal position for a new child node
   */
  public generateChildNodePosition(
    parentNode: Node,
    childIndex: number,
    totalChildren: number,
    existingNodes: Record<string, Node> = {},
    direction: 'horizontal' | 'vertical' | 'radial' = 'horizontal'
  ): NodePosition {
    const parentPosition = { x: parentNode.x, y: parentNode.y };
    
    // Get initial position from viewport manager
    let childPosition = this.viewportManager.calculateChildPosition(
      parentPosition,
      childIndex,
      totalChildren,
      direction
    );

    // Check for conflicts with existing nodes
    const maxAttempts = 10;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      let hasConflict = false;
      
      for (const existingNode of Object.values(existingNodes)) {
        const existingPos = { x: existingNode.x, y: existingNode.y };
        const distance = this.calculateDistance(childPosition, existingPos);
        const minDistance = 150; // Minimum separation
        
        if (distance < minDistance) {
          hasConflict = true;
          break;
        }
      }
      
      if (!hasConflict) {
        break;
      }
      
      // Adjust position and try again
      const offsetAngle = (attempts * Math.PI) / 4; // Rotate by 45 degrees each attempt
      const offsetDistance = 50 + attempts * 25;
      
      childPosition = {
        x: parentPosition.x + Math.cos(offsetAngle) * offsetDistance,
        y: parentPosition.y + Math.sin(offsetAngle) * offsetDistance
      };
      
      childPosition = this.viewportManager.ensureNodeVisible(childPosition);
      attempts++;
    }

    return childPosition;
  }

  /**
   * Validate and adjust root node position
   */
  public validateRootNodePosition(currentPosition?: NodePosition): NodePosition {
    // Root node should be centered in viewport
    const optimalPosition = this.viewportManager.getRootNodePosition();
    
    if (!currentPosition) {
      return optimalPosition;
    }

    // Check if current position is reasonable
    const validation = this.validateNodePosition(currentPosition);
    
    if (validation.isValid) {
      return currentPosition;
    }

    // If current position has issues, use optimal position
    console.log('NodePositionValidator: Root node position adjusted to viewport center');
    return optimalPosition;
  }

  /**
   * Get recommended layout bounds for a set of nodes
   */
  public getLayoutBounds(nodes: Record<string, Node>): {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
    width: number;
    height: number;
    center: NodePosition;
  } {
    if (Object.keys(nodes).length === 0) {
      const center = this.viewportManager.getViewportCenter();
      return {
        minX: center.x,
        maxX: center.x,
        minY: center.y,
        maxY: center.y,
        width: 0,
        height: 0,
        center
      };
    }

    const positions = Object.values(nodes).map(node => ({ x: node.x, y: node.y }));
    const xs = positions.map(p => p.x);
    const ys = positions.map(p => p.y);

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      minX,
      maxX,
      minY,
      maxY,
      width: maxX - minX,
      height: maxY - minY,
      center: {
        x: (minX + maxX) / 2,
        y: (minY + maxY) / 2
      }
    };
  }
}

// Singleton instance
let validatorInstance: NodePositionValidator | null = null;

/**
 * Get the global NodePositionValidator instance
 */
export function getNodePositionValidator(): NodePositionValidator {
  if (!validatorInstance) {
    validatorInstance = new NodePositionValidator();
  }
  return validatorInstance;
}
