import React, { useRef, useEffect, useState } from 'react';
import SendIcon from '@mui/icons-material/Send';
import IconButton from '@mui/material/IconButton';

interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

// Simple API check - just ping the health endpoint once
const checkApi = async (): Promise<boolean> => {
  try {
    const response = await fetch('/api/health', { 
      method: 'GET',
      signal: AbortSignal.timeout(3000) // 3 second timeout
    });
    return response.ok;
  } catch {
    return false;
  }
};

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChange,
  onSubmit,
  disabled = false,
  placeholder = "Type your message..."
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [apiOnline, setApiOnline] = useState(true); // Default to online

  // Check API status once on mount
  useEffect(() => {
    checkApi().then(setApiOnline);
  }, []);

  // Focus the textarea when component mounts
  useEffect(() => {
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 100);
  }, [disabled]);

  // Handle keyboard input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    e.stopPropagation();
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim()) {
        onSubmit(value.trim());
        onChange('');
      }
    }
  };

  // Handle textarea input
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  // Handle send button click
  const handleSendClick = () => {
    if (value.trim()) {
      onSubmit(value.trim());
      onChange('');
    }
  };

  return (
    <div className="message-input-container" style={{ position: 'relative' }}>
      <textarea
        ref={textareaRef}
        id="governance-input"
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className="message-input"
        style={{
          flex: 1,
          minHeight: '24px',
          maxHeight: '150px',
          padding: '8px 12px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          resize: 'none',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          backgroundColor: disabled ? '#f5f5f5' : 'white'
        }}
        autoFocus
      />
      <IconButton
        onClick={handleSendClick}
        disabled={disabled || !value.trim()}
        className="send-button"
        sx={{
          // Simple red/green coloring based on API status
          color: apiOnline ? '#4CAF50' : '#F44336', // Green if online, red if offline
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)'
          },
          '&:disabled': {
            color: '#bbb'
          }
        }}
      >
        <SendIcon />
      </IconButton>
    </div>
  );
};

export default MessageInput;