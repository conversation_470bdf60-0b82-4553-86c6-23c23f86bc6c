from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import datetime
import sys

# Import routes
from .routes import llm, mindmap, test, memory
from .routes import logging as logging_routes

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
os.makedirs(logs_dir, exist_ok=True)

# Set up logging to file with timestamp in filename
log_file = os.path.join(logs_dir, f"mindback_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("mindback")
logger.setLevel(logging.DEBUG)  # Set logger to DEBUG level for more detailed logs

# Create the FastAPI app
app = FastAPI(
    title="MindBack API",
    description="Backend API for MindBack application",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins during development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(llm.router)
app.include_router(mindmap.router)
app.include_router(test.router)
app.include_router(logging_routes.router)
app.include_router(memory.router)

@app.get("/")
async def root():
    """
    Root endpoint with API information
    """
    return {
        "api": "MindBack API",
        "version": "1.0.0",
        "endpoints": {
            "health_check": "/api/health",
            "llm_chat": "/api/llm/chat",
            "logging": "/api/logging/events",
            "memory": "/api/memory"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "api_version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development")
    }

@app.get("/api/health")
async def api_health_check():
    """API health check endpoint specifically for frontend connectivity testing"""
    return {
        "status": "ok",
        "api_version": "1.0.0",
        "cors_enabled": True,
        "server_time": datetime.datetime.now().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development")
    }

# Run with uvicorn
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)

logger.info("MindBack API initialized")
logger.info(f"Logs being written to: {log_file}")