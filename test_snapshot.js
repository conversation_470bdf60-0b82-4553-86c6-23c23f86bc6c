// Test script to verify snapshot functionality
// Run this in the browser console

console.log('=== Snapshot Functionality Test ===');

// Test 1: Check if AutoSnapshotService is available
console.log('1. Checking AutoSnapshotService availability...');
if (window.autoSnapshot) {
    console.log('✅ AutoSnapshotService is available');
    console.log('Config:', window.autoSnapshot.getConfig());
} else {
    console.log('❌ AutoSnapshotService not found');
}

// Test 2: Check if manual trigger function is available
console.log('2. Checking manual trigger function...');
if (window.triggerSnapshot) {
    console.log('✅ Manual trigger function is available');
} else {
    console.log('❌ Manual trigger function not found');
}

// Test 3: Try to trigger a manual snapshot
console.log('3. Attempting to trigger manual snapshot...');
if (window.triggerSnapshot) {
    try {
        window.triggerSnapshot('test-manual').then(() => {
            console.log('✅ Manual snapshot triggered successfully');
        }).catch(error => {
            console.log('❌ Manual snapshot failed:', error);
        });
    } catch (error) {
        console.log('❌ Error triggering manual snapshot:', error);
    }
} else {
    console.log('❌ Cannot test manual snapshot - function not available');
}

// Test 4: Check governance input element
console.log('4. Checking governance input element...');
const governanceInput = document.getElementById('governance-input');
if (governanceInput) {
    console.log('✅ Governance input found:', {
        tagName: governanceInput.tagName,
        id: governanceInput.id,
        className: governanceInput.className
    });
} else {
    console.log('❌ Governance input not found');
    // Try to find it by class
    const inputByClass = document.querySelector('.message-input');
    if (inputByClass) {
        console.log('Found input by class:', {
            tagName: inputByClass.tagName,
            id: inputByClass.id,
            className: inputByClass.className
        });
    }
}

// Test 5: Simulate Enter key press on governance input
console.log('5. Simulating Enter key press...');
if (governanceInput) {
    const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true,
        cancelable: true
    });
    
    console.log('Dispatching Enter key event...');
    governanceInput.dispatchEvent(enterEvent);
    console.log('✅ Enter key event dispatched');
} else {
    console.log('❌ Cannot simulate Enter key - governance input not found');
}

// Test 6: Check backend API
console.log('6. Testing backend API...');
fetch('/api/memory/snapshots', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        mindbook_name: 'test-api',
        snapshot_data: {
            test: 'data',
            timestamp: new Date().toISOString()
        }
    })
})
.then(response => {
    if (response.ok) {
        return response.json();
    } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
})
.then(data => {
    console.log('✅ Backend API test successful:', data);
})
.catch(error => {
    console.log('❌ Backend API test failed:', error);
});

console.log('=== Test Complete ===');
console.log('Check the console output above for results.');
console.log('Also check the backend/snapshots directory for any created files.');
