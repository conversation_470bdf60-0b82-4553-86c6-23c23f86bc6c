/**
 * UnifiedLayoutManager.ts
 * 
 * SINGLE SOURCE OF TRUTH for all layout operations
 * Enforces governance rules and prevents layout chaos
 */

import { LayoutStrategyType, LayoutRequest, LayoutResponse, DEFAULT_LAYOUT_CONFIG, LayoutConfig } from '../types/LayoutTypes';
// REMOVED: LayoutGovernanceService (deleted conflicting system)
import { Node, Connection } from '../state/MindMapStore';

// Layout strategy implementations
interface LayoutStrategy {
  readonly name: LayoutStrategyType;
  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config?: LayoutConfig
  ): Record<string, Node>;
}

// Left-to-right layout implementation
class LeftToRightLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'leftToRight';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);

    // Calculate layout bounds first
    let minX = 0, maxX = 0, minY = 0, maxY = 0;
    let firstNode = true;

    levels.forEach((level, levelIndex) => {
      const levelNodes = level;
      const totalHeight = levelNodes.length * (config.nodeHeight + config.verticalSpacing);
      const startY = -totalHeight / 2;

      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;

        const x = levelIndex * config.levelSpacing;
        const y = startY + nodeIndex * (config.nodeHeight + config.verticalSpacing);

        // Track bounds
        if (firstNode) {
          minX = x;
          maxX = x + config.nodeWidth;
          minY = y;
          maxY = y + config.nodeHeight;
          firstNode = false;
        } else {
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x + config.nodeWidth);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y + config.nodeHeight);
        }

        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    // Center the layout if centerLayout is enabled
    if (config.centerLayout) {
      const layoutWidth = maxX - minX;
      const layoutHeight = maxY - minY;
      const centerOffsetX = -layoutWidth / 2;
      const centerOffsetY = -layoutHeight / 2;

      Object.keys(updatedNodes).forEach(nodeId => {
        const node = updatedNodes[nodeId];
        if (node && levels.some(level => level.includes(nodeId))) {
          updatedNodes[nodeId] = {
            ...node,
            x: node.x + centerOffsetX,
            y: node.y + centerOffsetY
          };
        }
      });

      console.log('LeftToRightLayoutStrategy: Centered layout', {
        originalBounds: { minX, maxX, minY, maxY },
        layoutSize: { width: layoutWidth, height: layoutHeight },
        centerOffset: { x: centerOffsetX, y: centerOffsetY }
      });
    }

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      // Find children
      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Top-down layout implementation
class TopDownLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'topDown';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);

    // Calculate layout bounds first
    let minX = 0, maxX = 0, minY = 0, maxY = 0;
    let firstNode = true;

    levels.forEach((level, levelIndex) => {
      const levelNodes = level;
      const totalWidth = levelNodes.length * (config.nodeWidth + config.horizontalSpacing);
      const startX = -totalWidth / 2;

      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;

        const x = startX + nodeIndex * (config.nodeWidth + config.horizontalSpacing);
        const y = levelIndex * config.levelSpacing;

        // Track bounds
        if (firstNode) {
          minX = x;
          maxX = x + config.nodeWidth;
          minY = y;
          maxY = y + config.nodeHeight;
          firstNode = false;
        } else {
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x + config.nodeWidth);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y + config.nodeHeight);
        }

        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    // Center the layout if centerLayout is enabled
    if (config.centerLayout) {
      const layoutWidth = maxX - minX;
      const layoutHeight = maxY - minY;
      const centerOffsetX = -layoutWidth / 2;
      const centerOffsetY = -layoutHeight / 2;

      Object.keys(updatedNodes).forEach(nodeId => {
        const node = updatedNodes[nodeId];
        if (node && levels.some(level => level.includes(nodeId))) {
          updatedNodes[nodeId] = {
            ...node,
            x: node.x + centerOffsetX,
            y: node.y + centerOffsetY
          };
        }
      });

      console.log('TopDownLayoutStrategy: Centered layout', {
        originalBounds: { minX, maxX, minY, maxY },
        layoutSize: { width: layoutWidth, height: layoutHeight },
        centerOffset: { x: centerOffsetX, y: centerOffsetY }
      });
    }

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Radial layout implementation
class RadialLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'radial';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);
    
    // Position root at center
    updatedNodes[rootId] = { ...updatedNodes[rootId], x: 0, y: 0 };
    
    levels.forEach((level, levelIndex) => {
      if (levelIndex === 0) return; // Skip root level
      
      const levelNodes = level;
      const radius = levelIndex * 150; // Increasing radius per level
      const angleStep = (2 * Math.PI) / levelNodes.length;
      
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        const angle = nodeIndex * angleStep;
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);
        
        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Bottom-up layout implementation
class BottomUpLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'bottomUp';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);

    // Calculate layout bounds first
    let minX = 0, maxX = 0, minY = 0, maxY = 0;
    let firstNode = true;

    // Reverse the levels for bottom-up layout
    const reversedLevels = [...levels].reverse();

    reversedLevels.forEach((level, reversedIndex) => {
      const levelIndex = levels.length - 1 - reversedIndex; // Convert back to original index
      const levelNodes = level;
      const totalWidth = levelNodes.length * (config.nodeWidth + config.horizontalSpacing);
      const startX = -totalWidth / 2;

      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;

        const x = startX + nodeIndex * (config.nodeWidth + config.horizontalSpacing);
        const y = -levelIndex * config.levelSpacing; // Negative Y for bottom-up

        // Track bounds
        if (firstNode) {
          minX = x;
          maxX = x + config.nodeWidth;
          minY = y;
          maxY = y + config.nodeHeight;
          firstNode = false;
        } else {
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x + config.nodeWidth);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y + config.nodeHeight);
        }

        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    // Center the layout if centerLayout is enabled
    if (config.centerLayout) {
      const layoutWidth = maxX - minX;
      const layoutHeight = maxY - minY;
      const centerOffsetX = -layoutWidth / 2;
      const centerOffsetY = -layoutHeight / 2;

      Object.keys(updatedNodes).forEach(nodeId => {
        const node = updatedNodes[nodeId];
        if (node && levels.some(level => level.includes(nodeId))) {
          updatedNodes[nodeId] = {
            ...node,
            x: node.x + centerOffsetX,
            y: node.y + centerOffsetY
          };
        }
      });

      console.log('BottomUpLayoutStrategy: Centered layout', {
        originalBounds: { minX, maxX, minY, maxY },
        layoutSize: { width: layoutWidth, height: layoutHeight },
        centerOffset: { x: centerOffsetX, y: centerOffsetY }
      });
    }

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;

      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Compact left-to-right layout implementation
class CompactLeftToRightLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'compactLeftToRight';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);

    // Use compact spacing
    const compactConfig = {
      ...config,
      horizontalSpacing: Math.max(config.horizontalSpacing * (config.siblingCompactionFactor || 0.6), config.minimumHorizontalSpacing || 30),
      verticalSpacing: Math.max(config.verticalSpacing * (config.siblingCompactionFactor || 0.6), config.minimumVerticalSpacing || 25),
      levelSpacing: Math.max(config.levelSpacing * (config.levelCompactionFactor || 0.7), 120)
    };

    // Calculate layout bounds first
    let minX = 0, maxX = 0, minY = 0, maxY = 0;
    let firstNode = true;

    levels.forEach((level, levelIndex) => {
      const levelNodes = level;
      const totalHeight = levelNodes.length * (compactConfig.nodeHeight + compactConfig.verticalSpacing);
      const startY = -totalHeight / 2;

      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;

        const x = levelIndex * compactConfig.levelSpacing;
        const y = startY + nodeIndex * (compactConfig.nodeHeight + compactConfig.verticalSpacing);

        // Track bounds
        if (firstNode) {
          minX = x;
          maxX = x + compactConfig.nodeWidth;
          minY = y;
          maxY = y + compactConfig.nodeHeight;
          firstNode = false;
        } else {
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x + compactConfig.nodeWidth);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y + compactConfig.nodeHeight);
        }

        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    // Center the layout if centerLayout is enabled
    if (config.centerLayout) {
      const layoutWidth = maxX - minX;
      const layoutHeight = maxY - minY;
      const centerOffsetX = -layoutWidth / 2;
      const centerOffsetY = -layoutHeight / 2;

      Object.keys(updatedNodes).forEach(nodeId => {
        const node = updatedNodes[nodeId];
        if (node && levels.some(level => level.includes(nodeId))) {
          updatedNodes[nodeId] = {
            ...node,
            x: node.x + centerOffsetX,
            y: node.y + centerOffsetY
          };
        }
      });

      console.log('CompactLeftToRightLayoutStrategy: Centered layout', {
        originalBounds: { minX, maxX, minY, maxY },
        layoutSize: { width: layoutWidth, height: layoutHeight },
        centerOffset: { x: centerOffsetX, y: centerOffsetY },
        compactSpacing: {
          horizontal: compactConfig.horizontalSpacing,
          vertical: compactConfig.verticalSpacing,
          level: compactConfig.levelSpacing
        }
      });
    }

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;

      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

/**
 * Unified Layout Manager - Single point of control for all layout operations
 */
export class UnifiedLayoutManager {
  private static instance: UnifiedLayoutManager;
  // REMOVED: private governance: LayoutGovernanceService; (deleted conflicting system)
  private strategies: Map<LayoutStrategyType, LayoutStrategy> = new Map();
  private lastLayoutTime: Map<string, number> = new Map();

  static getInstance(): UnifiedLayoutManager {
    if (!this.instance) {
      this.instance = new UnifiedLayoutManager();
    }
    return this.instance;
  }

  constructor() {
    // REMOVED: this.governance = LayoutGovernanceService.getInstance(); (deleted conflicting system)
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    this.strategies.set('leftToRight', new LeftToRightLayoutStrategy());
    this.strategies.set('topDown', new TopDownLayoutStrategy());
    this.strategies.set('bottomUp', new BottomUpLayoutStrategy());
    this.strategies.set('radial', new RadialLayoutStrategy());
    this.strategies.set('compactLeftToRight', new CompactLeftToRightLayoutStrategy());

    console.log('UnifiedLayoutManager: Initialized layout strategies:', Array.from(this.strategies.keys()));
  }

  /**
   * SINGLE ENTRY POINT for all layout changes
   * Enforces governance rules and prevents chaos
   */
  async requestLayoutChange(request: LayoutRequest): Promise<LayoutResponse> {
    const timestamp = Date.now();
    
    console.log(`[UnifiedLayoutManager] Layout change requested:`, {
      strategy: request.strategy,
      sheetId: request.sheetId,
      origin: request.requestOrigin,
      reason: request.reason
    });

    // REMOVED: GOVERNANCE VALIDATION (deleted conflicting system)
    // Layout requests are now allowed by default

    // Rate limiting to prevent excessive changes
    const lastTime = this.lastLayoutTime.get(request.sheetId) || 0;
    const timeSinceLastLayout = timestamp - lastTime;
    if (timeSinceLastLayout < 500 && request.requestOrigin === 'auto') {
      console.warn(`[UnifiedLayoutManager] Layout request RATE LIMITED for sheet ${request.sheetId}`);
      return {
        success: false,
        strategy: request.strategy,
        reason: 'Rate limited - too frequent layout changes',
        timestamp
      };
    }

    try {
      // Apply the layout strategy
      const success = await this.applyLayoutStrategy(request);
      
      if (success) {
        this.lastLayoutTime.set(request.sheetId, timestamp);
        
        // REMOVED: Store user preference (deleted conflicting system)
        // User preferences are no longer stored in governance
        
        console.log(`[UnifiedLayoutManager] Layout successfully applied: ${request.strategy} for sheet ${request.sheetId}`);
      }

      return {
        success,
        strategy: request.strategy,
        reason: success ? 'Layout applied successfully' : 'Layout application failed',
        timestamp
      };
    } catch (error) {
      console.error(`[UnifiedLayoutManager] Error applying layout:`, error);
      return {
        success: false,
        strategy: request.strategy,
        reason: `Layout error: ${error.message}`,
        timestamp
      };
    }
  }

  /**
   * Check if layout change is allowed without executing it
   */
  canChangeLayout(request: Partial<LayoutRequest>): boolean {
    if (!request.strategy || !request.sheetId) return false;
    
    const fullRequest: LayoutRequest = {
      strategy: request.strategy,
      sheetId: request.sheetId,
      requestOrigin: request.requestOrigin || 'user'
    };
    
    // REMOVED: governance validation (deleted conflicting system)
    return true; // All layout requests are now allowed
  }

  /**
   * Get the preferred layout strategy for a sheet
   */
  getPreferredStrategy(sheetId: string): LayoutStrategyType {
    // REMOVED: governance.getPreferredStrategy (deleted conflicting system)
    return 'leftToRight'; // Default strategy
  }

  private async applyLayoutStrategy(request: LayoutRequest): Promise<boolean> {
    const strategy = this.strategies.get(request.strategy);
    if (!strategy) {
      throw new Error(`Layout strategy '${request.strategy}' not found`);
    }

    // Get the store for this sheet
    const store = this.getSheetStore(request.sheetId);
    if (!store) {
      throw new Error(`Store not found for sheet: ${request.sheetId}`);
    }

    const state = store.getState();
    const { nodes, connections, rootNodeId } = state;

    if (!rootNodeId || !nodes[rootNodeId]) {
      console.warn(`[UnifiedLayoutManager] No root node found for sheet ${request.sheetId}`);
      return false;
    }

    // Calculate new layout
    const updatedNodes = strategy.calculateLayout(nodes, connections, rootNodeId);

    // Update the store with new positions
    Object.entries(updatedNodes).forEach(([nodeId, node]) => {
      if (nodes[nodeId] && (nodes[nodeId].x !== node.x || nodes[nodeId].y !== node.y)) {
        console.log(`[UnifiedLayoutManager] Updating node ${nodeId} position: (${node.x}, ${node.y})`);
        store.getState().updateNode(nodeId, { x: node.x, y: node.y });
      }
    });

    // Update current layout strategy in store
    store.setState({ currentLayoutStrategy: request.strategy });

    // CRITICAL FIX: Center the viewport on the layout after positioning nodes
    setTimeout(() => {
      console.log(`[UnifiedLayoutManager] Attempting to center viewport for sheet ${request.sheetId}`);
      this.centerViewportOnLayout(request.sheetId, updatedNodes);
    }, 200);

    return true;
  }

  private getSheetStore(sheetId: string): any {
    // This will be connected to the store factory
    // For now, we'll use a global method that should be implemented
    if (typeof window !== 'undefined' && (window as any).getSheetMindMapStore) {
      const store = (window as any).getSheetMindMapStore(sheetId);
      if (!store) {
        console.error(`[UnifiedLayoutManager] Store factory returned null for sheet: ${sheetId}`);
        console.log(`[UnifiedLayoutManager] Available stores:`, Object.keys((window as any).useMindMapStoreRegistry?.getState?.().stores || {}));
      }
      return store;
    }
    
    console.error(`[UnifiedLayoutManager] Store factory not available for sheet: ${sheetId}`);
    console.log(`[UnifiedLayoutManager] window.getSheetMindMapStore:`, typeof (window as any).getSheetMindMapStore);
    return null;
  }

  /**
   * Center the viewport on the layout after nodes are positioned
   */
  private centerViewportOnLayout(sheetId: string, nodes: Record<string, Node>): void {
    try {
      const store = this.getSheetStore(sheetId);
      if (!store) {
        console.warn(`[UnifiedLayoutManager] Cannot center viewport: store not found for sheet ${sheetId}`);
        return;
      }

      const nodePositions = Object.values(nodes);
      if (nodePositions.length === 0) {
        console.warn(`[UnifiedLayoutManager] Cannot center viewport: no nodes found`);
        return;
      }

      // Calculate the bounds of all nodes
      let minX = nodePositions[0].x;
      let maxX = nodePositions[0].x + (nodePositions[0].width || 180);
      let minY = nodePositions[0].y;
      let maxY = nodePositions[0].y + (nodePositions[0].height || 70);

      nodePositions.forEach(node => {
        const nodeWidth = node.width || 180;
        const nodeHeight = node.height || 70;
        minX = Math.min(minX, node.x);
        maxX = Math.max(maxX, node.x + nodeWidth);
        minY = Math.min(minY, node.y);
        maxY = Math.max(maxY, node.y + nodeHeight);
      });

      // Calculate the center of all nodes
      const centerX = (minX + maxX) / 2;
      const centerY = (minY + maxY) / 2;

      // Calculate viewport center (get actual viewport dimensions)
      const viewportWidth = window.innerWidth || 1200;
      const viewportHeight = window.innerHeight || 800;

      // Account for any UI elements (approximate)
      const effectiveViewportWidth = viewportWidth * 0.8; // Account for governance box
      const effectiveViewportHeight = viewportHeight * 0.9; // Account for header/footer

      const viewportCenterX = effectiveViewportWidth / 2;
      const viewportCenterY = effectiveViewportHeight / 2;

      // Calculate the position needed to center the layout in the viewport
      const newPosition = {
        x: viewportCenterX - centerX,
        y: viewportCenterY - centerY
      };

      console.log(`[UnifiedLayoutManager] Centering viewport on layout:`, {
        nodeBounds: { minX, maxX, minY, maxY },
        layoutCenter: { x: centerX, y: centerY },
        viewportCenter: { x: viewportCenterX, y: viewportCenterY },
        newPosition,
        nodeCount: nodePositions.length
      });

      // Update the store position to center the layout
      const currentState = store.getState();
      console.log(`[UnifiedLayoutManager] Current position before centering:`, currentState.position);

      store.getState().setPosition(newPosition);

      // Verify the position was set
      const newState = store.getState();
      console.log(`[UnifiedLayoutManager] New position after centering:`, newState.position);

    } catch (error) {
      console.error(`[UnifiedLayoutManager] Error centering viewport:`, error);
    }
  }
}

// Export singleton instance
export const unifiedLayoutManager = UnifiedLayoutManager.getInstance(); 