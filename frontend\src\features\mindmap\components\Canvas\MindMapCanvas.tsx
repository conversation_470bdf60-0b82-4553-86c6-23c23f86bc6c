/**
 * MindMapCanvas.tsx
 *
 * Canvas component for rendering the mind map.
 * Uses react-konva for rendering.
 */

import React, { useRef, useEffect, useState } from 'react';
import { Layer, Group } from 'react-konva';
import NodeComponent from './NodeComponent';
import ConnectionComponent from './ConnectionComponent';
import StageCompatWrapper from '../../../../components/MindMap/components/Canvas/StageCompatWrapper';
import '../../../../core/services/KeyboardManager'; // Import to ensure it's initialized
import './MindMapCanvas.css';
import { mindMapGovernance } from '../../../../core/governance/MindMapGovernance';
// Import viewport management system
import { getViewportManager } from '../../../../core/viewport/ViewportManager';
import { getNodePositionValidator } from '../../../../core/viewport/NodePositionValidator';

// Log window dimensions to help with debugging
console.log('MindMapCanvas: Window dimensions:', {
  width: window.innerWidth,
  height: window.innerHeight
});

// Cast Konva components to any to bypass TypeScript errors
const KonvaLayer = Layer as any;
const KonvaGroup = Group as any;

interface MindMapCanvasProps {
  width: number;
  height: number;
  sheetId: string; // Add sheetId to props
  store?: any; // Store prop passed from wrapper
}

const MindMapCanvas: React.FC<MindMapCanvasProps> = ({ width, height, sheetId, store }) => {
  const stageRef = useRef<any>(null);
  const storeRef = useRef<any>(store);

  // Store the store reference in a ref to avoid re-renders
  if (store !== storeRef.current) {
    storeRef.current = store;
  }

  // Use state to store values from the store and subscribe to changes
  const [storeState, setStoreState] = useState(() => {
    return storeRef.current ? storeRef.current.getState() : {
      nodes: {},
      connections: [],
      position: { x: 0, y: 0 },
      scale: 1,
      selectedNodeId: null,
      rootNodeId: null
    };
  });

  // Subscribe to store changes
  useEffect(() => {
    if (storeRef.current) {
      console.log(`MindMapCanvas: Subscribing to store changes for sheet: ${sheetId}`);
      
      // Initial state update
      setStoreState(storeRef.current.getState());
      
      // Subscribe to store changes
      const unsubscribe = storeRef.current.subscribe((state: any) => {
        console.log(`MindMapCanvas: Store updated for sheet ${sheetId}:`, {
          nodeCount: Object.keys(state.nodes || {}).length,
          connectionCount: (state.connections || []).length,
          position: state.position,
          scale: state.scale
        });
        setStoreState(state);
      });

      return () => {
        if (unsubscribe) unsubscribe();
      };
    }
  }, [sheetId]);

  // Log the sheet ID and store to help with debugging
  useEffect(() => {
    if (storeRef.current) {
      console.log(`MindMapCanvas: Using sheet-specific store for sheet: ${sheetId}`);
      console.log(`MindMapCanvas: Store has ${Object.keys(storeRef.current.getState().nodes).length} nodes`);
    }
  }, [sheetId]);

  // Extract values from store state
  const nodes = storeState.nodes || {};
  const connections = storeState.connections || [];
  const position = storeState.position || { x: 0, y: 0 };
  const scale = storeState.scale || 1;
  const selectedNodeId = storeState.selectedNodeId;
  
  // Get action functions from the store (these don't change)
  const selectNode = storeRef.current?.getState().selectNode || (() => {});
  const setPosition = storeRef.current?.getState().setPosition || (() => {});
  const setScale = storeRef.current?.getState().setScale || (() => {});
  const updateNode = storeRef.current?.getState().updateNode || (() => {});

  // Focus the stage when a node is selected, but only if this sheet is active
  useEffect(() => {
    if (selectedNodeId && stageRef.current) {
      console.log('Node selected in sheet:', sheetId, 'nodeId:', selectedNodeId);

      // Check if this sheet is active before focusing
      const isActive = document.querySelector(`[data-sheet-id="${sheetId}"]`)?.classList.contains('active');

      if (!isActive) {
        console.log('Not focusing stage because sheet is not active:', sheetId);
        return;
      }

      // Focus with a slight delay to ensure the DOM is ready
      setTimeout(() => {
        if (stageRef.current) {
          const container = stageRef.current.getStage().container();
          container.focus();
          console.log('Stage focused for sheet:', sheetId, 'success:', document.activeElement === container);

          // Check governance rules before auto-centering
          const config = mindMapGovernance.getSheetConfig(sheetId);
          
          if (!config.positioning.viewport.centerOnNodeSelect && config.positioning.viewport.preventJumpOnClick) {
            console.log('MindMapCanvas: Auto-centering disabled by governance - preventing viewport jump');
            return;
          }

          // Ensure the selected node is visible (only if governance allows)
          const selectedNode = nodes[selectedNodeId];
          if (selectedNode && config.positioning.viewport.centerOnNodeSelect) {
            console.log('Selected node found:', selectedNode.id, selectedNode.text);

            // If the node is off-screen, center the view on it
            const stage = stageRef.current.getStage();
            const stageWidth = stage.width();
            const stageHeight = stage.height();

            // Calculate node position relative to stage
            const nodeX = selectedNode.x * scale + position.x;
            const nodeY = selectedNode.y * scale + position.y;

            // Check if node is outside visible area
            const isOffScreen =
              nodeX < 0 ||
              nodeY < 0 ||
              nodeX > stageWidth ||
              nodeY > stageHeight;

            if (isOffScreen) {
              console.log('Selected node is off-screen, centering view (governance allows)');

              // Calculate new position to center the node
              const newPosition = {
                x: stageWidth / 2 - selectedNode.x,
                y: stageHeight / 2 - selectedNode.y
              };

              // Validate viewport change with governance
              if (mindMapGovernance.validateViewportChange(sheetId, { position: newPosition })) {
                // Update position in store and local state
                setPosition(newPosition);
                if (storeRef.current) {
                  storeRef.current.getState().setPosition(newPosition);
                }
                
                console.log('MindMapCanvas: Centered view on selected node:', selectedNode.id, 'new position:', newPosition);
              }
            }
          }

          // Force a re-render to ensure the node is visually selected
          setTimeout(() => {
            try {
              // Dispatch a custom event to notify that a node was selected
              const event = new CustomEvent('mindback:node_selected', {
                detail: {
                  nodeId: selectedNodeId,
                  sheetId: sheetId
                }
              });
              document.dispatchEvent(event);
            } catch (error) {
              console.error('Error updating node selection state:', error);
            }
          }, 50);
        }
      }, 100);
    }
  }, [selectedNodeId, nodes, scale, position, sheetId, setPosition]);

  // We no longer need a global Tab key handler here
  // The KeyboardManager service now handles all keyboard events centrally
  // This ensures that only the active mindsheet receives keyboard events

  // Public method to focus the stage
  const focusStage = () => {
    if (stageRef.current) {
      const container = stageRef.current.getStage().container();
      container.focus();
      console.log('Stage focused via public method');
      return true;
    }
    return false;
  };

  // Focus the stage on mount
  useEffect(() => {
    // Only proceed if this sheet is active
    const isActive = document.querySelector(`[data-sheet-id="${sheetId}"]`)?.classList.contains('active');
    if (!isActive) {
      console.log('MindMapCanvas: Not focusing because sheet is not active:', sheetId);
      return;
    }

    // Focus with a delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      focusStage();
    }, 100);

    return () => clearTimeout(timer);
  }, [sheetId]);

  // Validate and fix node positions when nodes change
  useEffect(() => {
    if (nodes && Object.keys(nodes).length > 0) {
      validateAndFixNodePositions();
    }
  }, [nodes]);

  // Function to validate and fix off-screen node positions
  const validateAndFixNodePositions = () => {
    if (!nodes || !storeRef.current) return;

    const viewportManager = getViewportManager();
    let hasChanges = false;

    console.log('MindMapCanvas: Validating node positions for viewport visibility');

    Object.values(nodes).forEach((node: any) => {
      // Check if node is visible in viewport
      const isVisible = viewportManager.isNodeVisible({ x: node.x, y: node.y });

      if (!isVisible) {
        console.log(`MindMapCanvas: Node ${node.id} is off-screen at (${node.x}, ${node.y}), repositioning...`);

        // Get adjusted position
        const adjustedPosition = viewportManager.ensureNodeVisible({ x: node.x, y: node.y });

        // Update the node position in the store
        const storeState = storeRef.current.getState();
        if (storeState.updateNode) {
          storeState.updateNode(node.id, {
            x: adjustedPosition.x,
            y: adjustedPosition.y
          });

          hasChanges = true;
          console.log(`MindMapCanvas: Repositioned node ${node.id} to (${adjustedPosition.x}, ${adjustedPosition.y})`);
        }
      }
    });

    if (hasChanges) {
      console.log('MindMapCanvas: Node positions updated for viewport visibility');

      // Force a re-render after position updates
      setTimeout(() => {
        if (storeRef.current) {
          const storeState = storeRef.current.getState();
          if (storeState.updateLayout) {
            storeState.updateLayout('tree');
          }
        }
      }, 100);
    }
  };

  // Listen for the refresh_canvas event only
  useEffect(() => {
    // Handler for refresh_canvas event
    const handleRefreshCanvas = (event: any) => {
      // Check if this event is for this sheet or for all sheets
      if (event.detail.sheetId === sheetId || event.detail.sheetId === 'current') {
        console.log('MindMapCanvas: Received refresh_canvas event for sheet:', sheetId);

        // Force a re-render by updating a state variable
        setTimeout(() => {
          if (storeRef.current) {
            try {
              // Get the current state
              const currentState = storeRef.current.getState();

              // Log the current state before refresh
              console.log('MindMapCanvas: Current state before refresh:', {
                nodeCount: Object.keys(currentState.nodes || {}).length,
                connectionCount: (currentState.connections || []).length,
                selectedNodeId: currentState.selectedNodeId,
                rootNodeId: currentState.rootNodeId
              });

              // Check if we have a specific node ID in the event
              if (event.detail.nodeId) {
                console.log('MindMapCanvas: Refresh event includes node ID:', event.detail.nodeId);

                // Check if this node exists in our store
                const node = currentState.nodes[event.detail.nodeId];
                if (node) {
                  console.log('MindMapCanvas: Node found in store:', node.id, node.text);

                  // Select this node to make it visible
                  currentState.selectNode(node.id);
                } else {
                  console.log('MindMapCanvas: Node not found in store:', event.detail.nodeId);
                }
              }

              // Also update the layout to ensure proper positioning
              if (currentState.updateLayout) {
                currentState.updateLayout('tree');
                console.log('MindMapCanvas: Updated layout after refresh event');
              } else {
                console.warn('MindMapCanvas: updateLayout function not found in store');
              }
            } catch (error) {
              console.error('MindMapCanvas: Error refreshing canvas:', error);
            }
          }
        }, 50);
      }
    };

    // Add event listener
    document.addEventListener('mindback:refresh_canvas', handleRefreshCanvas);

    // Clean up
    return () => {
      document.removeEventListener('mindback:refresh_canvas', handleRefreshCanvas);
    };
  }, [sheetId]);

  // Handle stage drag start
  const handleDragStart = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag started');
    }
  };

  // Handle stage drag move
  const handleDragMove = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      // Update position in real-time for smoother dragging
      const newPos = stageRef.current.position();
      setPosition(newPos);
    }
  };

  // Handle stage drag end
  const handleDragEnd = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag ended');
      const newPos = stageRef.current.position();
      setPosition(newPos);
    }
  };

  // Handle wheel zoom
  const handleWheel = (e: any) => {
    // Prevent default scrolling
    e.evt.preventDefault();

    // Get current sheet for governance validation
    const currentSheet = document.querySelector('.mind-sheet.active');
    const sheetId = currentSheet?.getAttribute('data-sheet-id');
    
    if (!sheetId) return;

    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    // Zoom direction
    const direction = e.evt.deltaY > 0 ? -1 : 1;

    // Zoom factor
    const zoomFactor = 1.1;
    const newScale = direction > 0 ? oldScale * zoomFactor : oldScale / zoomFactor;

    // Validate scale change with governance
    if (!mindMapGovernance.validateViewportChange(sheetId, { scale: newScale })) {
      console.log('MindMapCanvas: Scale change rejected by governance');
      return;
    }

    // Apply the new scale
    setScale(newScale);

    // Calculate new position
    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    // Validate and apply position change
    if (mindMapGovernance.validateViewportChange(sheetId, { position: newPos })) {
      setPosition(newPos);
    }

    console.log('MindMapCanvas: Zoom applied - scale:', newScale, 'position:', newPos);
  };

  // Handle keyboard shortcuts for the canvas
  // Tab key is now handled by the KeyboardManager service
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Prevent default for handled keys
    const handledKeys = ['Tab', 'Enter', 'Delete', 'Backspace', 'Escape'];
    if (handledKeys.includes(e.key)) {
      e.preventDefault();
    }

    console.log('MindMapCanvas: Key pressed:', e.key, 'for sheet:', sheetId);

    switch (e.key) {
      case 'Tab':
        if (selectedNodeId && nodes[selectedNodeId]) {
          console.log('MindMapCanvas: Tab key - adding child node');

          const parentNode = nodes[selectedNodeId];

          // Use viewport-aware positioning for child node
          const positionValidator = getNodePositionValidator();
          const childPosition = positionValidator.generateChildNodePosition(
            parentNode,
            0, // First child index
            1, // Total children count
            nodes,
            'horizontal'
          );

          // Add child node with viewport-aware position
          const childId = storeRef.current?.getState().addNode(
            selectedNodeId,
            'New Node',
            childPosition.x,
            childPosition.y
          );
          
          if (childId) {
            // Select the new child node
            selectNode(childId);
            
            console.log('MindMapCanvas: Child node added and selected:', childId);
          }
        }
        break;
        
      case 'Enter':
        if (selectedNodeId && nodes[selectedNodeId]) {
          console.log('MindMapCanvas: Enter key - adding sibling node');
          
          const currentNode = nodes[selectedNodeId];
          
          // Find the parent of the current node
          const parentConnection = connections.find(conn => conn.to === selectedNodeId);
          const parentId = parentConnection ? parentConnection.from : null;
          
          if (parentId && nodes[parentId]) {
            const parentNode = nodes[parentId];

            // Use viewport-aware positioning for sibling node
            const positionValidator = getNodePositionValidator();
            const siblingChildren = connections.filter(conn => conn.from === parentId);
            const siblingPosition = positionValidator.generateChildNodePosition(
              parentNode,
              siblingChildren.length, // Next sibling index
              siblingChildren.length + 1, // Total children count including new one
              nodes,
              'horizontal'
            );

            // Add sibling node with viewport-aware position
            const siblingId = storeRef.current?.getState().addNode(
              parentId,
              'New Node',
              siblingPosition.x,
              siblingPosition.y
            );
            
            if (siblingId) {
              // Select the new sibling node
              selectNode(siblingId);
              
              console.log('MindMapCanvas: Sibling node added and selected:', siblingId);
            }
          } else {
            console.log('MindMapCanvas: No parent found for sibling creation');
          }
        }
        break;
    }
  };

  // Render loading message if no nodes
  if (!nodes || Object.keys(nodes).length === 0) {
    console.log('MindMapCanvas: No nodes found, showing loading state for sheet:', sheetId);
    return (
      <div className="loading-canvas">
        <div className="initializing-mindmap">
          <div className="loading-spinner"></div>
          <p>Initializing canvas for sheet {sheetId}...</p>
          <button
            onClick={() => {
              console.log('Forcing sheet-specific store update for sheet:', sheetId);

              // Check if we have saved state in the MindBookStore
              try {
                // Import the functions directly to avoid circular dependencies
                import('../../../../core/state/MindBookStore').then(({ useMindBookStore }) => {
                  const mindBookStore = useMindBookStore.getState();
                  const savedState = mindBookStore.getSheetState(sheetId);

                  if (savedState && savedState.nodes && Object.keys(savedState.nodes).length > 0) {
                    console.log('MindMapCanvas: Found saved state in MindBookStore, applying it');

                    // Apply the saved state to the store
                    const storeState = storeRef.current.getState();

                    // Apply nodes
                    Object.entries(savedState.nodes).forEach(([nodeId, node]) => {
                      storeState.updateNode(nodeId, node as any);
                    });

                    // Apply connections
                    if (savedState.connections && Array.isArray(savedState.connections)) {
                      savedState.connections.forEach((conn: any) => {
                        if (conn.from && conn.to) {
                          storeState.addConnection(conn.from, conn.to, {
                            color: conn.color,
                            width: conn.width,
                            style: conn.style
                          });
                        }
                      });
                    }

                    // Apply position and scale
                    if (savedState.position) {
                      storeState.setPosition(savedState.position);
                    }

                    if (savedState.scale) {
                      storeState.setScale(savedState.scale);
                    }

                    // Apply root node ID
                    if (savedState.rootNodeId) {
                      storeState.rootNodeId = savedState.rootNodeId;
                    }
                  }
                }).catch(error => {
                  console.error('MindMapCanvas: Error importing MindBookStore:', error);
                });
              } catch (error) {
                console.error('MindMapCanvas: Error applying saved state:', error);
              }

              // Force the sheet-specific store to update its layout
              if (storeRef.current) {
                const { updateLayout } = storeRef.current.getState();
                if (updateLayout) {
                  updateLayout('tree');
                }
              }
            }}
            style={{ marginTop: '10px', padding: '5px 10px' }}
          >
            Refresh Canvas
          </button>
        </div>
      </div>
    );
  }

  console.log('MindMapCanvas: Rendering with nodes:', Object.keys(nodes).length);

  // Use the position from the store for the Stage
  // The store position represents where the stage should be to center the content
  console.log(`MindMapCanvas: Using store position (${position.x}, ${position.y}) for stage - width: ${width}, height: ${height}`);

  return (
    <div className="mindmap-canvas-container" style={{ width: '100%', height: '100%' }}>
      <StageCompatWrapper
        ref={stageRef}
        width={width}
        height={height}
        draggable
        x={position.x}
        y={position.y}
        scaleX={scale}
        scaleY={scale}
        onDragStart={handleDragStart}
        onDragMove={handleDragMove}
        onDragEnd={handleDragEnd}
        onWheel={handleWheel}
        onKeyDown={handleKeyDown}
        onClick={(e) => {
          // Only handle stage clicks, not node clicks
          if (e.target === stageRef.current.getStage()) {
            // Focus the stage on click
            if (stageRef.current) {
              stageRef.current.getStage().container().focus();
              console.log('Stage clicked and focused');

              // Deselect any selected node when clicking on empty space
              if (selectedNodeId) {
                selectNode(null);
                console.log('Deselected node on stage click');
              }
            }
          }
        }}
        tabIndex={0} // Make the stage focusable
      >
      <KonvaLayer>
        {/* Render connections */}
        <KonvaGroup>
          {connections.map((connection: any) => (
            <ConnectionComponent
              key={connection.id}
              connection={connection}
              fromNode={nodes[connection.from]}
              toNode={nodes[connection.to]}
            />
          ))}
        </KonvaGroup>

        {/* Render nodes */}
        <KonvaGroup>
          {Object.values(nodes).map((node: any) => (
            <NodeComponent
              key={node.id}
              node={node}
              isSelected={node.id === selectedNodeId}
              onClick={() => selectNode(node.id)}
              updateNode={updateNode}
              selectNode={selectNode}
            />
          ))}
        </KonvaGroup>
      </KonvaLayer>
    </StageCompatWrapper>
    </div>
  );
};

export default MindMapCanvas;
