/**
 * NodeComponent.tsx
 *
 * Component for rendering a node in the mind map.
 */

import React from 'react';
import { Group, Rect, Text, Ellipse, RegularPolygon } from 'react-konva';
import { Node } from '../../../../core/types/MindMapTypes';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';
// Import the store factory instead of direct store
import { getMindMapStore } from '../../../../core/state/MindMapStoreFactory';
import { mindMapGovernance } from '../../../../core/governance/MindMapGovernance';

interface NodeComponentProps {
  node: Node;
  isSelected: boolean;
  onClick: () => void;
  updateNode: (id: string, updates: Partial<Node>) => void;
  selectNode: (id: string) => void;
}

const NodeComponent: React.FC<NodeComponentProps> = ({ node, isSelected, onClick, updateNode, selectNode }) => {
  // Helper function to check if NodeBox is open
  const checkNodeboxOpen = () => {
    const nodeboxOverlay = document.querySelector('.nodebox-overlay');
    return !!nodeboxOverlay && window.getComputedStyle(nodeboxOverlay).display !== 'none';
  };

  // Handle drag start
  const handleDragStart = (e: any) => {
    // Check if dragging is allowed by governance
    const currentSheet = document.querySelector('.mind-sheet.active');
    const sheetId = currentSheet?.getAttribute('data-sheet-id');
    
    if (sheetId) {
      const config = mindMapGovernance.getSheetConfig(sheetId);
      if (!config.nodes.dragging.enabled) {
        e.evt.preventDefault();
        return false;
      }
    }

    // Prevent dragging if NodeBox is open
    if (checkNodeboxOpen()) {
      console.log('NodeComponent: Drag prevented - NodeBox is open');
      e.evt.preventDefault();
      return false;
    }

    // Prevent dragging if node is in editing mode
    if (node.metadata?.isEditing) {
      console.log('NodeComponent: Drag prevented - Node is in editing mode');
      e.evt.preventDefault();
      return false;
    }

    console.log('NodeComponent: Drag started for node:', node.id);
    
    // Store initial position for proper drag validation
    const initialPos = { x: node.x, y: node.y };
    e.target.setAttrs({ initialPos });

    // Prevent default behavior that might cause width resizing
    if (e.evt) {
      e.evt.stopPropagation();
    }

    // Register the drag start event
    RegistrationManager.registerEvent(EventType.NODE_MOVED, {
      id: node.id,
      action: 'drag_start',
      position: initialPos
    });
  };

  // Simplified drag move handler
  const handleDragMove = (e: any) => {
    // Simple position update without complex validation
    const newX = e.target.x();
    const newY = e.target.y();

    // Update node position
    updateNode(node.id, { x: newX, y: newY });

    console.log(`[NodeComponent] 📍 Node ${node.id} dragged to (${newX}, ${newY})`);
  };

  // Simplified drag end handler
  const handleDragEnd = (e: any) => {
    const newX = e.target.x();
    const newY = e.target.y();

    console.log(`[NodeComponent] 🏁 Drag ended for node ${node.id} at (${newX}, ${newY})`);

    // Simple final position update
    updateNode(node.id, {
      x: newX,
      y: newY,
      metadata: {
        ...node.metadata,
        positioned: true,
        lastMoved: Date.now()
      }
    });

    // Register event
    RegistrationManager.registerEvent(EventType.NODE_MOVED, {
      id: node.id,
      action: 'drag_end',
      position: { x: newX, y: newY }
    });
  };

  // Render different shapes based on node.shape
  const renderShape = () => {
    // Check both the isSelected prop and the node's own isSelected property
    const nodeIsSelected = isSelected || node.isSelected;

    // Log selection state for debugging
    console.log(`Node ${node.id} isSelected:`, nodeIsSelected,
      'prop:', isSelected, 'node.isSelected:', node.isSelected);

    // DISABLED: Competing shape event handlers - use Group handlers only
    const shapeEventHandlers = {
      // No individual shape handlers to prevent conflicts
    };

    // Use blue for selected nodes (original styling)
    const selectedColor = '#3498db';
    const selectedStrokeWidth = 3;
    const selectedShadowBlur = 10;
    const selectedShadowOpacity = 0.5;

    const shapeProps = {
      width: nodeWidth,
      height: nodeHeight,
      fill: node.color || '#ffffff',
      stroke: nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50'),
      strokeWidth: nodeIsSelected ? selectedStrokeWidth : 1,
      shadowColor: nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)',
      shadowBlur: nodeIsSelected ? selectedShadowBlur : 5,
      shadowOffset: { x: 0, y: 2 },
      shadowOpacity: nodeIsSelected ? selectedShadowOpacity : 0.5,
      cornerRadius: 5,
      // Add event handlers to the shape itself
      ...shapeEventHandlers
    };

    switch (node.shape) {
      case 'ellipse':
        return (
          <Ellipse
            radiusX={nodeWidth / 2}
            radiusY={nodeHeight / 2}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'diamond':
        return (
          <RegularPolygon
            sides={4}
            radius={Math.min(nodeWidth, nodeHeight) / 2}
            rotation={45}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'hexagon':
        return (
          <RegularPolygon
            sides={6}
            radius={Math.min(nodeWidth, nodeHeight) / 2}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'rectangle':
      default:
        return <Rect {...shapeProps} />;
    }
  };

  // Handle double-click to open the node dialog
  const handleDblClick = (e: any) => {
    console.log(`[NodeComponent] 🖱️🖱️ DOUBLE-CLICK EVENT on node ${node.id}:`, {
      nodeText: node.text?.substring(0, 20),
      position: { x: node.x, y: node.y },
      eventType: e.type,
      timestamp: Date.now()
    });

    // Prevent default browser behavior (text selection)
    if (e && e.evt) {
      e.evt.preventDefault();
      e.cancelBubble = true; // Stop event propagation
      e.evt.stopPropagation(); // Additional stop propagation
    }

    // First make sure the node is selected with multiple attempts
    selectNode(node.id); // Immediate selection

    // Get the sheet ID from the parent element
    const sheetElement = document.querySelector('.mind-sheet.active');
    const sheetId = sheetElement ? sheetElement.getAttribute('data-sheet-id') : null;

    if (!sheetId) {
      console.error('NODEBOX DEBUG: Could not find active sheet ID');
      return;
    }

    try {
      // Get the sheet-specific store using the factory
      const store = getMindMapStore(sheetId);

      // Update the node metadata with isEditing flag using the sheet-specific store
      store.getState().updateNode(node.id, {
        metadata: {
          ...(node.metadata || {}),
          isEditing: true // This flag will be used by NodeBox to determine if it should open
        }
      });

      console.log('NODEBOX DEBUG: Updated node metadata with isEditing flag using sheet-specific store');

      // Make the node non-draggable immediately
      const nodeGroup = document.querySelector(`[data-node-id="${node.id}"]`);
      if (nodeGroup) {
        (nodeGroup as HTMLElement).setAttribute('draggable', 'false');
        console.log('NODEBOX DEBUG: Set node to non-draggable');
      }

      // Register the node selection event
      RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
        id: node.id,
        text: node.text,
        position: { x: node.x, y: node.y }
      });

      // Register the node opening event
      RegistrationManager.registerEvent(EventType.NODE_OPENED, { id: node.id });

      // Dispatch a direct event to open the NodeBox
      // This is a more reliable way to trigger the NodeBox to open
      const openNodeBoxEvent = new CustomEvent('mindback:open_nodebox', {
        detail: {
          nodeId: node.id,
          sheetId: sheetId
        }
      });
      document.dispatchEvent(openNodeBoxEvent);
      console.log('NODEBOX DEBUG: Dispatched open_nodebox event for node:', node.id);

    } catch (error) {
      console.error('NODEBOX DEBUG: Error in double-click handler:', error);
    }
  };

  // Ensure node has valid position
  const nodeX = node.x || 0;
  const nodeY = node.y || 0;
  const nodeWidth = node.width || 200;
  const nodeHeight = node.height || 100;

  // COMPREHENSIVE DEBUG: Log node position and viewport info
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // NOTE: This is checking world coordinates, not screen coordinates
  // Screen coordinates would be: nodeX + stageX, nodeY + stageY
  const isOffScreenWorld = nodeX < -200 || nodeX > viewportWidth + 200 || nodeY < -200 || nodeY > viewportHeight + 200;

  // Only log for the first few nodes to avoid console spam
  const shouldLog = Math.random() < 0.1; // Log ~10% of renders
  if (shouldLog) {
    console.log(`[NodeComponent] Rendering node ${node.id}:`, {
      worldPosition: { x: nodeX, y: nodeY },
      dimensions: { width: nodeWidth, height: nodeHeight },
      viewport: { width: viewportWidth, height: viewportHeight },
      isOffScreenWorld,
      text: node.text?.substring(0, 20) + '...'
    });
  }

  // CRITICAL WARNING: Alert if node is off-screen (but only occasionally to avoid spam)
  if (isOffScreenWorld && shouldLog) {
    console.warn(`[NodeComponent] ⚠️ NODE OFF-SCREEN (world coords): ${node.id} at (${nodeX}, ${nodeY}) - viewport: ${viewportWidth}x${viewportHeight}`);
  }

  // Track if we're in editing mode to prevent dragging
  const isEditing = node.metadata?.isEditing || false;

  // Determine if the node should be draggable
  const shouldBeDraggable = !isEditing && !checkNodeboxOpen();

  console.log(`Node ${node.id} draggable state:`, {
    isEditing,
    nodeboxOpen: checkNodeboxOpen(),
    shouldBeDraggable
  });

  // Simplified drag bound function - only basic canvas constraints
  const dragBoundFunc = (pos: { x: number; y: number }) => {
    // Simple canvas boundary constraints only
    const canvasWidth = window.innerWidth;
    const canvasHeight = window.innerHeight;
    const nodeWidth = node.width || 200;
    const nodeHeight = node.height || 100;

    return {
      x: Math.max(-nodeWidth/2, Math.min(pos.x, canvasWidth - nodeWidth/2)),
      y: Math.max(-nodeHeight/2, Math.min(pos.y, canvasHeight - nodeHeight/2))
    };
  };

  return (
    <Group
      x={nodeX}
      y={nodeY}
      draggable={shouldBeDraggable}
      dragBoundFunc={shouldBeDraggable ? dragBoundFunc : undefined}
      data-node-id={node.id}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onClick={(e) => {
        console.log(`[NodeComponent] 🖱️ SINGLE CLICK on node ${node.id}`);

        // Prevent event bubbling to avoid conflicts
        e.cancelBubble = true;
        if (e.evt) {
          e.evt.stopPropagation();
          e.evt.preventDefault();
        }

        // Only handle selection - no dragging initiation
        if (onClick) onClick();
        selectNode(node.id);

        // Register event
        RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
          id: node.id,
          text: node.text,
          position: { x: nodeX, y: nodeY }
        });
      }}
      onDblClick={(e) => {
        console.log(`[NodeComponent] 🖱️🖱️ DOUBLE CLICK on node ${node.id}`);

        // Prevent event bubbling
        e.cancelBubble = true;
        if (e.evt) {
          e.evt.stopPropagation();
          e.evt.preventDefault();
        }

        // Call double click handler
        handleDblClick(e);
      }}
    >
      {renderShape()}
      {/* Node Path (displayed separately from the title) */}
      <Text
        text={node.metadata?.nodePath || '1.0'}
        width={nodeWidth}
        height={20}
        y={-nodeHeight/2 + 15}
        align="center"
        verticalAlign="middle"
        fontSize={10}
        fontFamily="Arial, sans-serif"
        fill="#666666"
        listening={false} // Disable text event handling to prevent conflicts
      />
      {/* Node Title (without path prefix) */}
      <Text
        text={node.text || 'Untitled Node'}
        width={nodeWidth}
        height={nodeHeight}
        align="center"
        verticalAlign="middle"
        fontSize={14}
        fontFamily="Arial"
        fill="#333333"
        padding={5}
        ellipsis
        listening={false} // Disable text event handling to prevent conflicts
      />
    </Group>
  );
};

export default NodeComponent;
