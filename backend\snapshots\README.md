# MindBack Snapshots Directory

This directory contains automatic snapshots generated when users press Enter in input boxes.

## File Naming Convention
- Format: `[mindbook_name]_[timestamp].json`
- Example: `My_Project_20250117_153045.json`

## Automatic Management
- Maximum 3 versions per mindbook
- Older snapshots are automatically deleted
- Triggered by Enter key in:
  - Governance box
  - ChatFork inputs
  - Node input boxes
  - MindSheet inputs

## Snapshot Content
Each snapshot contains:
- Complete MindBook state
- Conversation threads
- Memory entries
- Context settings
- User events
- localStorage data

## API Endpoints
- `POST /api/memory/snapshots` - Save snapshot
- `GET /api/memory/snapshots/{mindbook_name}` - Get all snapshots
- `GET /api/memory/snapshots/{mindbook_name}/latest` - Get latest snapshot
- `DELETE /api/memory/snapshots/{filename}` - Delete snapshot
- `GET /api/memory/snapshots` - Get storage statistics
