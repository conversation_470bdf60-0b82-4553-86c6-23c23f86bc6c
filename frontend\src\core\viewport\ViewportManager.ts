/**
 * ViewportManager.ts
 * 
 * Manages viewport-aware coordinate transformations and node positioning
 * for MindBack mindmap functionality. Ensures all nodes remain visible
 * within the viewport boundaries.
 * 
 * Created: 2025-06-24
 * Part of: Phase 1 - Node Positioning System Overhaul
 */

export interface ViewportBounds {
  width: number;
  height: number;
  centerX: number;
  centerY: number;
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

export interface NodePosition {
  x: number;
  y: number;
}

export interface NodeDimensions {
  width: number;
  height: number;
}

export interface ViewportConfig {
  padding: number; // Minimum distance from viewport edges
  nodeDefaultWidth: number;
  nodeDefaultHeight: number;
  centeringEnabled: boolean;
}

/**
 * ViewportManager handles all viewport-aware positioning logic
 * for mindmap nodes, ensuring they remain visible and properly positioned.
 */
export class ViewportManager {
  private config: ViewportConfig;
  private bounds: ViewportBounds;

  constructor(config: Partial<ViewportConfig> = {}) {
    this.config = {
      padding: 50,
      nodeDefaultWidth: 200,
      nodeDefaultHeight: 100,
      centeringEnabled: true,
      ...config
    };

    this.updateViewportBounds();
    
    // Listen for window resize events
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * Update viewport bounds based on canvas dimensions
   */
  private updateViewportBounds(canvasWidth?: number, canvasHeight?: number): void {
    // Use provided canvas dimensions or fall back to window size
    const width = canvasWidth || window.innerWidth;
    const height = canvasHeight || window.innerHeight;

    this.bounds = {
      width,
      height,
      centerX: width / 2,
      centerY: height / 2,
      minX: this.config.padding,
      maxX: width - this.config.padding,
      minY: this.config.padding,
      maxY: height - this.config.padding
    };

    console.log('ViewportManager: Updated bounds:', this.bounds);
  }

  /**
   * Update viewport bounds with specific canvas dimensions
   */
  public updateCanvasBounds(width: number, height: number): void {
    this.updateViewportBounds(width, height);
  }

  /**
   * Handle window resize events
   */
  private handleResize(): void {
    this.updateViewportBounds();
  }

  /**
   * Get current viewport bounds
   */
  public getViewportBounds(): ViewportBounds {
    return { ...this.bounds };
  }

  /**
   * Get viewport center position
   */
  public getViewportCenter(): NodePosition {
    return {
      x: this.bounds.centerX,
      y: this.bounds.centerY
    };
  }

  /**
   * Check if a node position is within viewport boundaries
   */
  public isNodeVisible(position: NodePosition, dimensions?: NodeDimensions): boolean {
    const nodeWidth = dimensions?.width || this.config.nodeDefaultWidth;
    const nodeHeight = dimensions?.height || this.config.nodeDefaultHeight;

    const nodeLeft = position.x - nodeWidth / 2;
    const nodeRight = position.x + nodeWidth / 2;
    const nodeTop = position.y - nodeHeight / 2;
    const nodeBottom = position.y + nodeHeight / 2;

    return (
      nodeLeft >= this.bounds.minX &&
      nodeRight <= this.bounds.maxX &&
      nodeTop >= this.bounds.minY &&
      nodeBottom <= this.bounds.maxY
    );
  }

  /**
   * Ensure a node position is visible within viewport boundaries
   * Returns adjusted position if needed
   */
  public ensureNodeVisible(position: NodePosition, dimensions?: NodeDimensions): NodePosition {
    const nodeWidth = dimensions?.width || this.config.nodeDefaultWidth;
    const nodeHeight = dimensions?.height || this.config.nodeDefaultHeight;

    let adjustedX = position.x;
    let adjustedY = position.y;

    // Calculate node boundaries
    const halfWidth = nodeWidth / 2;
    const halfHeight = nodeHeight / 2;

    // Adjust X position if needed
    if (adjustedX - halfWidth < this.bounds.minX) {
      adjustedX = this.bounds.minX + halfWidth;
    } else if (adjustedX + halfWidth > this.bounds.maxX) {
      adjustedX = this.bounds.maxX - halfWidth;
    }

    // Adjust Y position if needed
    if (adjustedY - halfHeight < this.bounds.minY) {
      adjustedY = this.bounds.minY + halfHeight;
    } else if (adjustedY + halfHeight > this.bounds.maxY) {
      adjustedY = this.bounds.maxY - halfHeight;
    }

    const wasAdjusted = adjustedX !== position.x || adjustedY !== position.y;
    
    if (wasAdjusted) {
      console.log('ViewportManager: Adjusted node position:', {
        original: position,
        adjusted: { x: adjustedX, y: adjustedY },
        reason: 'viewport_bounds'
      });
    }

    return { x: adjustedX, y: adjustedY };
  }

  /**
   * Calculate optimal position for a child node relative to parent
   * Ensures child remains visible while maintaining logical positioning
   */
  public calculateChildPosition(
    parentPosition: NodePosition,
    childIndex: number,
    totalChildren: number,
    direction: 'horizontal' | 'vertical' | 'radial' = 'horizontal'
  ): NodePosition {
    const spacing = 150; // Base spacing between nodes
    let childPosition: NodePosition;

    switch (direction) {
      case 'horizontal':
        childPosition = {
          x: parentPosition.x + spacing,
          y: parentPosition.y + (childIndex - (totalChildren - 1) / 2) * spacing
        };
        break;

      case 'vertical':
        childPosition = {
          x: parentPosition.x + (childIndex - (totalChildren - 1) / 2) * spacing,
          y: parentPosition.y + spacing
        };
        break;

      case 'radial':
        const angle = (2 * Math.PI * childIndex) / totalChildren;
        const radius = Math.max(spacing, totalChildren * 30); // Dynamic radius
        childPosition = {
          x: parentPosition.x + Math.cos(angle) * radius,
          y: parentPosition.y + Math.sin(angle) * radius
        };
        break;

      default:
        childPosition = { ...parentPosition };
    }

    // Ensure the calculated position is visible
    return this.ensureNodeVisible(childPosition);
  }

  /**
   * Get optimal root node position (centered in viewport)
   */
  public getRootNodePosition(): NodePosition {
    return this.getViewportCenter();
  }

  /**
   * Calculate stage position to center a specific node in viewport
   * Used for viewport navigation and centering operations
   */
  public calculateStagePositionToCenterNode(nodePosition: NodePosition): NodePosition {
    return {
      x: this.bounds.centerX - nodePosition.x,
      y: this.bounds.centerY - nodePosition.y
    };
  }

  /**
   * Transform stage coordinates to viewport coordinates
   */
  public stageToViewport(stagePosition: NodePosition, stageOffset: NodePosition): NodePosition {
    return {
      x: stagePosition.x + stageOffset.x,
      y: stagePosition.y + stageOffset.y
    };
  }

  /**
   * Transform viewport coordinates to stage coordinates
   */
  public viewportToStage(viewportPosition: NodePosition, stageOffset: NodePosition): NodePosition {
    return {
      x: viewportPosition.x - stageOffset.x,
      y: viewportPosition.y - stageOffset.y
    };
  }

  /**
   * Validate and adjust multiple node positions to prevent overlap
   * and ensure all nodes remain visible
   */
  public validateNodeLayout(nodes: Record<string, NodePosition>): Record<string, NodePosition> {
    const adjustedNodes: Record<string, NodePosition> = {};
    const nodeIds = Object.keys(nodes);

    // First pass: ensure all nodes are within viewport
    for (const nodeId of nodeIds) {
      adjustedNodes[nodeId] = this.ensureNodeVisible(nodes[nodeId]);
    }

    // Second pass: resolve overlaps (simplified approach)
    // This is a basic implementation - can be enhanced with more sophisticated algorithms
    for (let i = 0; i < nodeIds.length; i++) {
      for (let j = i + 1; j < nodeIds.length; j++) {
        const nodeA = adjustedNodes[nodeIds[i]];
        const nodeB = adjustedNodes[nodeIds[j]];
        
        const distance = Math.sqrt(
          Math.pow(nodeA.x - nodeB.x, 2) + Math.pow(nodeA.y - nodeB.y, 2)
        );
        
        const minDistance = this.config.nodeDefaultWidth + 50; // Minimum separation
        
        if (distance < minDistance) {
          // Push nodes apart
          const angle = Math.atan2(nodeB.y - nodeA.y, nodeB.x - nodeA.x);
          const pushDistance = (minDistance - distance) / 2;
          
          adjustedNodes[nodeIds[i]] = this.ensureNodeVisible({
            x: nodeA.x - Math.cos(angle) * pushDistance,
            y: nodeA.y - Math.sin(angle) * pushDistance
          });
          
          adjustedNodes[nodeIds[j]] = this.ensureNodeVisible({
            x: nodeB.x + Math.cos(angle) * pushDistance,
            y: nodeB.y + Math.sin(angle) * pushDistance
          });
        }
      }
    }

    return adjustedNodes;
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    window.removeEventListener('resize', this.handleResize.bind(this));
  }
}

// Singleton instance for global use
let viewportManagerInstance: ViewportManager | null = null;

/**
 * Get the global ViewportManager instance
 */
export function getViewportManager(): ViewportManager {
  if (!viewportManagerInstance) {
    viewportManagerInstance = new ViewportManager();
  }
  return viewportManagerInstance;
}

/**
 * Initialize ViewportManager with custom config
 */
export function initializeViewportManager(config: Partial<ViewportConfig>): ViewportManager {
  if (viewportManagerInstance) {
    viewportManagerInstance.destroy();
  }
  viewportManagerInstance = new ViewportManager(config);
  return viewportManagerInstance;
}
