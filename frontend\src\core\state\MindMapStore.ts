/**
 * MindMapStore.ts
 *
 * Consolidated state management for the MindMap feature.
 * This is the single source of truth for all MindMap state.
 */

import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
// Import from unified types instead of old LayoutManager
import { LayoutStrategyType } from '../types/LayoutTypes';
import { UnifiedLayoutManager } from '../layout/UnifiedLayoutManager';
import { LayoutGovernanceService } from '../services/LayoutGovernanceService';
import { getViewportManager } from '../viewport/ViewportManager';

// Types
export interface Node {
  id: string;
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  borderColor: string;
  shape: 'rectangle' | 'ellipse' | 'diamond' | 'hexagon';
  description?: string;
  hatContributions?: {
    blue: boolean;
    white: boolean;
    red: boolean;
    black: boolean;
    yellow: boolean;
    green: boolean;
  };
  metadata?: {
    nodePath?: string;
    [key: string]: any;
  };
}

export interface Connection {
  id: string;
  from: string;
  to: string;
  color: string;
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
  curvature?: number;
  direction?: string;
  theme?: 'normal' | 'angled' | 'curved';
  lineStyle?: 'straight' | 'angled' | 'curved';
  thickness?: number;
  showArrow?: boolean;
  type?: string;
}

export interface Position {
  x: number;
  y: number;
}

// LLM Model types
export type LLMModel = 'gpt-4' | 'gpt-3.5-turbo' | 'claude-3' | 'claude-2' | 'local-model';

// Store state interface
export interface MindMapState {
  // Sheet identification - CRITICAL for governance
  sheetId: string;
  
  // Core state
  nodes: Record<string, Node>;
  connections: Connection[];
  selectedNodeId: string | null;
  
  // Layout and viewport
  position: Position;
  scale: number;
  currentLayoutStrategy: LayoutStrategyType; // Track current strategy

  // Canvas dimensions for viewport management
  canvasWidth?: number;
  canvasHeight?: number;
  
  // Project and metadata
  projectName: string;
  rootNodeId: string | null;
  
  // UI state
  showProjectDialog: boolean;
  showMindMapManager: boolean;
  
  // LLM configuration
  llmModel: LLMModel;
  
  // Actions
  initialize: (width: number, height: number, sheetId?: string) => void;
  
  // Node operations
  addNode: (parentId: string | null, text: string, x: number, y: number, options?: Partial<Node>) => string;
  updateNode: (id: string, updates: Partial<Node>) => void;
  deleteNode: (id: string) => void;
  selectNode: (id: string | null) => void;
  
  // Connection operations
  addConnection: (from: string, to: string, options?: Partial<Connection>) => string;
  updateConnection: (id: string, updates: Partial<Connection>) => void;
  deleteConnection: (id: string) => void;
  
  // Project operations
  createNewProject: (name: string) => string;
  deleteProject: () => void;
  
  // Layout operations - GOVERNANCE INTEGRATED
  updateLayout: (strategy: LayoutStrategyType, requestOrigin?: 'user' | 'system' | 'auto') => Promise<boolean>;
  getPreferredLayoutStrategy: () => LayoutStrategyType;
  canChangeLayout: (strategy: LayoutStrategyType) => boolean;
  
  // Viewport operations
  setPosition: (position: Position) => void;
  setScale: (scale: number) => void;
  setCanvasDimensions: (width: number, height: number) => void;
  
  // UI operations
  setShowProjectDialog: (show: boolean) => void;
  setShowMindMapManager: (show: boolean) => void;
  setProjectName: (name: string) => void;
  
  // LLM operations
  setLlmModel: (model: LLMModel) => void;
}

// Create the store factory
export const createMindMapStore = () => create<MindMapState>()((set, get) => ({
  // Initial state
  sheetId: '', // Will be set during initialization
  nodes: {},
  connections: [],
  selectedNodeId: null,
  position: { x: 0, y: 0 },
  scale: 1.0,
  currentLayoutStrategy: 'leftToRight', // Default strategy
  projectName: '',
  rootNodeId: null,
  showProjectDialog: false,
  showMindMapManager: false,
  llmModel: 'gpt-4',
  canvasWidth: undefined,
  canvasHeight: undefined,

  // Initialize the store with dimensions and sheet ID
  initialize: (width: number, height: number, sheetId?: string) => {
    const id = sheetId || `sheet-${Date.now()}`;
    console.log(`[MindMapStore] Initializing store for sheet: ${id} with dimensions: ${width}x${height}`);
    
    // Get preferred layout strategy from governance
    const governance = LayoutGovernanceService.getInstance();
    const preferredStrategy = governance.getPreferredStrategy(id);
    
    // Calculate center position immediately - no delays
    const centerX = width / 2;
    const centerY = height / 2;
    
    set({
      sheetId: id,
      nodes: {},
      connections: [],
      selectedNodeId: null,
      position: { x: centerX, y: centerY }, // Center immediately
      scale: 1.0,
      currentLayoutStrategy: preferredStrategy,
      projectName: '',
      rootNodeId: null,
      showProjectDialog: false,
      showMindMapManager: false
    });
    
    console.log(`[MindMapStore] Store initialized with centered position: ${centerX}, ${centerY}`);
  },

  // Node operations
  addNode: (parentId: string | null, text: string, x: number, y: number, options = {}) => {
    const id = uuidv4();
    const newNode: Node = {
      id,
      text,
      x,
      y,
      width: 180,
      height: 70,
      color: '#ffffff',
      borderColor: '#2c3e50',
      shape: 'rectangle',
      ...options
    };

    set(state => {
      const updatedNodes = { ...state.nodes, [id]: newNode };
      let updatedConnections = [...state.connections];
      let updatedRootNodeId = state.rootNodeId;

      // If this is the first node, make it the root
      if (!state.rootNodeId) {
        updatedRootNodeId = id;
      }

      // Create connection to parent if specified
      if (parentId && state.nodes[parentId]) {
        const connectionId = uuidv4();
        const connection: Connection = {
          id: connectionId,
          from: parentId,
          to: id,
          color: '#2c3e50',
          width: 2,
          style: 'solid'
        };
        updatedConnections.push(connection);
      }

      return {
        nodes: updatedNodes,
        connections: updatedConnections,
        rootNodeId: updatedRootNodeId
      };
    });

    console.log(`[MindMapStore] Added node: ${id} (${text})`);
    return id;
  },

  updateNode: (id: string, updates: Partial<Node>) => {
    set(state => ({
      nodes: {
        ...state.nodes,
        [id]: { ...state.nodes[id], ...updates }
      }
    }));
  },

  deleteNode: (id: string) => {
    set(state => {
      const { [id]: deletedNode, ...remainingNodes } = state.nodes;
      const updatedConnections = state.connections.filter(
        conn => conn.from !== id && conn.to !== id
      );

      return {
        nodes: remainingNodes,
        connections: updatedConnections,
        selectedNodeId: state.selectedNodeId === id ? null : state.selectedNodeId,
        rootNodeId: state.rootNodeId === id ? null : state.rootNodeId
      };
    });
  },

  selectNode: (id: string | null) => {
    set({ selectedNodeId: id });
  },

  // Connection operations
  addConnection: (from: string, to: string, options = {}) => {
    const id = uuidv4();
    const connection: Connection = {
      id,
      from,
      to,
      color: '#2c3e50',
      width: 2,
      style: 'solid',
      ...options
    };

    set(state => ({
      connections: [...state.connections, connection]
    }));

    return id;
  },

  updateConnection: (id: string, updates: Partial<Connection>) => {
    set(state => ({
      connections: state.connections.map(conn =>
        conn.id === id ? { ...conn, ...updates } : conn
      )
    }));
  },

  deleteConnection: (id: string) => {
    set(state => ({
      connections: state.connections.filter(conn => conn.id !== id)
    }));
  },

  // Project operations
  createNewProject: (name: string) => {
    const rootId = uuidv4();

    // Use viewport-aware positioning for root node
    const viewportManager = getViewportManager();

    // Get current store state to get canvas dimensions
    const currentState = get();

    // Update viewport manager with current canvas dimensions if available
    // This ensures the root node is positioned correctly for the current canvas size
    if (currentState.canvasWidth && currentState.canvasHeight) {
      viewportManager.updateCanvasBounds(currentState.canvasWidth, currentState.canvasHeight);
      console.log(`[MindMapStore] Updated viewport manager with canvas dimensions: ${currentState.canvasWidth}x${currentState.canvasHeight}`);
    } else {
      console.warn('[MindMapStore] No canvas dimensions available, using default viewport bounds');
    }

    const rootPosition = viewportManager.getRootNodePosition();
    console.log(`[MindMapStore] Root node will be positioned at: (${rootPosition.x}, ${rootPosition.y})`);

    const rootNode: Node = {
      id: rootId,
      text: name,
      x: rootPosition.x,
      y: rootPosition.y,
      width: 200,
      height: 100,
      color: '#ffffff',
      borderColor: '#2c3e50',
      shape: 'rectangle',
      metadata: {
        nodePath: '1.0'
      }
    };

    set({
      nodes: { [rootId]: rootNode },
      connections: [],
      selectedNodeId: rootId,
      rootNodeId: rootId,
      projectName: name,
      // FIXED: Keep existing centered position instead of resetting to (0,0)
      position: currentState.position,
      scale: 1.0
    });

    console.log(`[MindMapStore] Created new project: ${name} with root node: ${rootId} at centered position: ${currentState.position.x}, ${currentState.position.y}`);
    return rootId;
  },

  deleteProject: () => {
    set({
      nodes: {},
      connections: [],
      selectedNodeId: null,
      rootNodeId: null,
      projectName: '',
      position: { x: 0, y: 0 },
      scale: 1.0
    });
  },

  // GOVERNANCE-INTEGRATED LAYOUT OPERATIONS
  updateLayout: async (strategy: LayoutStrategyType, requestOrigin: 'user' | 'system' | 'auto' = 'user') => {
    const state = get();
    const { sheetId } = state;
    
    if (!sheetId) {
      console.error('[MindMapStore] Cannot update layout: sheetId not set');
      return false;
    }

    try {
      console.log(`[MindMapStore] Requesting layout change to ${strategy} for sheet ${sheetId} (origin: ${requestOrigin})`);
      
      const layoutManager = UnifiedLayoutManager.getInstance();
      const response = await layoutManager.requestLayoutChange({
        strategy,
        sheetId,
        requestOrigin,
        reason: `Layout change requested via store (${requestOrigin})`,
        preserveViewport: requestOrigin === 'user' // Preserve viewport for user-initiated changes
      });
      
      if (response.success) {
        // Update current strategy in state
        set({ currentLayoutStrategy: strategy });
        
        // Store user preference if user-initiated
        if (requestOrigin === 'user') {
          const governance = LayoutGovernanceService.getInstance();
          governance.setUserPreference(sheetId, strategy);
        }
        
        console.log(`[MindMapStore] Layout updated successfully to ${strategy} for sheet ${sheetId}`);
        return true;
      } else {
        console.warn(`[MindMapStore] Layout change rejected: ${response.reason}`);
        return false;
      }
    } catch (error) {
      console.error('[MindMapStore] Error updating layout:', error);
      return false;
    }
  },

  getPreferredLayoutStrategy: (): LayoutStrategyType => {
    const { sheetId } = get();
    if (!sheetId) {
      return 'leftToRight'; // Fallback
    }
    
    const governance = LayoutGovernanceService.getInstance();
    return governance.getPreferredStrategy(sheetId);
  },

  canChangeLayout: (strategy: LayoutStrategyType): boolean => {
    const { sheetId } = get();
    if (!sheetId) return false;
    
    const layoutManager = UnifiedLayoutManager.getInstance();
    return layoutManager.canChangeLayout({
      strategy,
      sheetId,
      requestOrigin: 'user'
    });
  },

  // Viewport operations
  setScale: (scale) => set({ scale }),

  setPosition: (position) => set({ position }),

  setCanvasDimensions: (width: number, height: number) => {
    console.log(`[MindMapStore] Setting canvas dimensions: ${width}x${height}`);
    set({ canvasWidth: width, canvasHeight: height });

    // Update viewport manager with new dimensions
    const viewportManager = getViewportManager();
    viewportManager.updateCanvasBounds(width, height);
  },

  setShowProjectDialog: (show) => set({ showProjectDialog: show }),

  setShowMindMapManager: (show) => {
    console.log('[MindMapStore] Setting showMindMapManager to:', show);
    set({ showMindMapManager: show });
  },

  setProjectName: (name) => set({ projectName: name }),

  setLlmModel: (model) => set({ llmModel: model })
}));

// Create and export the default store instance
export const useMindMapStore = createMindMapStore();
