"""
SnapshotStorageService.py

Handles automatic snapshot generation and storage with file management.
Saves snapshots as [mindbook]_[timestamp].json with maximum 3 versions per mindbook.
"""

import os
import json
import glob
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

class SnapshotStorageService:
    """
    Service for managing snapshot files with automatic cleanup
    """
    
    def __init__(self, snapshots_dir: str = None):
        # Use the specified directory path
        if snapshots_dir is None:
            # Get the project root directory (MindBack_V1)
            # From backend/api/services/snapshot_storage_service.py
            # Go up: services -> api -> backend -> MindBack_V1 (project root)
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent.parent
            self.snapshots_dir = project_root / "backend" / "snapshots"
        else:
            self.snapshots_dir = Path(snapshots_dir)

        # Ensure snapshots directory exists
        self.snapshots_dir.mkdir(parents=True, exist_ok=True)
        print(f"SnapshotStorageService: Using directory {self.snapshots_dir}")
        print(f"SnapshotStorageService: Directory exists: {self.snapshots_dir.exists()}")
        print(f"SnapshotStorageService: Directory is writable: {os.access(self.snapshots_dir, os.W_OK)}")
    
    def save_snapshot(self, mindbook_name: str, snapshot_data: Dict[str, Any]) -> str:
        """
        Save a snapshot with automatic versioning and cleanup
        
        Args:
            mindbook_name: Name of the mindbook (will be sanitized for filename)
            snapshot_data: The snapshot data to save
            
        Returns:
            str: Path to the saved snapshot file
        """
        try:
            # Sanitize mindbook name for filename
            safe_name = self._sanitize_filename(mindbook_name or "untitled")
            
            # Generate timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create filename
            filename = f"{safe_name}_{timestamp}.json"
            filepath = self.snapshots_dir / filename
            
            # Add metadata to snapshot
            enhanced_snapshot = {
                **snapshot_data,
                "snapshot_metadata": {
                    "mindbook_name": mindbook_name,
                    "saved_at": datetime.now().isoformat(),
                    "filename": filename,
                    "version": self._get_next_version_number(safe_name)
                }
            }
            
            # Save the snapshot
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(enhanced_snapshot, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"SnapshotStorageService: Saved snapshot {filename}")
            
            # Clean up old snapshots (keep only 3 most recent)
            self._cleanup_old_snapshots(safe_name)
            
            return str(filepath)
            
        except Exception as e:
            print(f"SnapshotStorageService: Error saving snapshot: {e}")
            raise e
    
    def get_snapshots_for_mindbook(self, mindbook_name: str) -> List[Dict[str, Any]]:
        """
        Get all snapshots for a specific mindbook
        
        Args:
            mindbook_name: Name of the mindbook
            
        Returns:
            List of snapshot metadata sorted by creation time (newest first)
        """
        try:
            safe_name = self._sanitize_filename(mindbook_name or "untitled")
            pattern = f"{safe_name}_*.json"
            snapshot_files = glob.glob(str(self.snapshots_dir / pattern))
            
            snapshots = []
            for filepath in snapshot_files:
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Extract metadata
                    metadata = data.get('snapshot_metadata', {})
                    file_stats = os.stat(filepath)
                    
                    snapshots.append({
                        'filename': os.path.basename(filepath),
                        'filepath': filepath,
                        'mindbook_name': metadata.get('mindbook_name', mindbook_name),
                        'saved_at': metadata.get('saved_at'),
                        'version': metadata.get('version', 0),
                        'file_size': file_stats.st_size,
                        'created_at': datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                        'modified_at': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })
                except Exception as e:
                    print(f"SnapshotStorageService: Error reading snapshot {filepath}: {e}")
                    continue
            
            # Sort by creation time (newest first)
            snapshots.sort(key=lambda x: x['created_at'], reverse=True)
            return snapshots
            
        except Exception as e:
            print(f"SnapshotStorageService: Error getting snapshots for {mindbook_name}: {e}")
            return []
    
    def load_snapshot(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        Load a specific snapshot by filename
        
        Args:
            filename: Name of the snapshot file
            
        Returns:
            Snapshot data or None if not found
        """
        try:
            filepath = self.snapshots_dir / filename
            
            if not filepath.exists():
                print(f"SnapshotStorageService: Snapshot file not found: {filename}")
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"SnapshotStorageService: Error loading snapshot {filename}: {e}")
            return None
    
    def get_latest_snapshot(self, mindbook_name: str) -> Optional[Dict[str, Any]]:
        """
        Get the most recent snapshot for a mindbook
        
        Args:
            mindbook_name: Name of the mindbook
            
        Returns:
            Latest snapshot data or None if not found
        """
        snapshots = self.get_snapshots_for_mindbook(mindbook_name)
        
        if not snapshots:
            return None
        
        # Load the latest snapshot
        latest = snapshots[0]
        return self.load_snapshot(latest['filename'])
    
    def delete_snapshot(self, filename: str) -> bool:
        """
        Delete a specific snapshot file
        
        Args:
            filename: Name of the snapshot file to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            filepath = self.snapshots_dir / filename
            
            if filepath.exists():
                os.remove(filepath)
                print(f"SnapshotStorageService: Deleted snapshot {filename}")
                return True
            else:
                print(f"SnapshotStorageService: Snapshot file not found: {filename}")
                return False
                
        except Exception as e:
            print(f"SnapshotStorageService: Error deleting snapshot {filename}: {e}")
            return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get statistics about snapshot storage
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            snapshot_files = glob.glob(str(self.snapshots_dir / "*.json"))
            
            total_size = 0
            mindbook_counts = {}
            
            for filepath in snapshot_files:
                file_stats = os.stat(filepath)
                total_size += file_stats.st_size
                
                # Extract mindbook name from filename
                filename = os.path.basename(filepath)
                mindbook_name = filename.split('_')[0] if '_' in filename else 'unknown'
                mindbook_counts[mindbook_name] = mindbook_counts.get(mindbook_name, 0) + 1
            
            return {
                'total_snapshots': len(snapshot_files),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'mindbook_counts': mindbook_counts,
                'snapshots_directory': str(self.snapshots_dir)
            }
            
        except Exception as e:
            print(f"SnapshotStorageService: Error getting storage stats: {e}")
            return {
                'total_snapshots': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'mindbook_counts': {},
                'snapshots_directory': str(self.snapshots_dir),
                'error': str(e)
            }
    
    def _sanitize_filename(self, name: str) -> str:
        """
        Sanitize a string to be safe for use as a filename
        
        Args:
            name: Original name
            
        Returns:
            Sanitized filename-safe string
        """
        # Replace invalid characters with underscores
        invalid_chars = '<>:"/\\|?*'
        sanitized = name
        
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
        
        # Remove extra spaces and replace with underscores
        sanitized = '_'.join(sanitized.split())
        
        # Limit length
        if len(sanitized) > 50:
            sanitized = sanitized[:50]
        
        # Ensure it's not empty
        if not sanitized:
            sanitized = "untitled"
        
        return sanitized
    
    def _get_next_version_number(self, safe_name: str) -> int:
        """
        Get the next version number for a mindbook
        
        Args:
            safe_name: Sanitized mindbook name
            
        Returns:
            Next version number
        """
        try:
            pattern = f"{safe_name}_*.json"
            existing_files = glob.glob(str(self.snapshots_dir / pattern))
            return len(existing_files) + 1
        except:
            return 1
    
    def _cleanup_old_snapshots(self, safe_name: str) -> None:
        """
        Keep only the 3 most recent snapshots for a mindbook
        
        Args:
            safe_name: Sanitized mindbook name
        """
        try:
            pattern = f"{safe_name}_*.json"
            snapshot_files = glob.glob(str(self.snapshots_dir / pattern))
            
            if len(snapshot_files) <= 3:
                return  # No cleanup needed
            
            # Sort by modification time (newest first)
            snapshot_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # Delete files beyond the 3 most recent
            files_to_delete = snapshot_files[3:]
            
            for filepath in files_to_delete:
                try:
                    os.remove(filepath)
                    filename = os.path.basename(filepath)
                    print(f"SnapshotStorageService: Cleaned up old snapshot {filename}")
                except Exception as e:
                    print(f"SnapshotStorageService: Error deleting old snapshot {filepath}: {e}")
                    
        except Exception as e:
            print(f"SnapshotStorageService: Error during cleanup for {safe_name}: {e}")
