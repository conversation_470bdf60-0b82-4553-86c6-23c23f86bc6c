# MindBack Taskbar Launcher - Simplified Version

A clean, simple Windows-native launcher for MindBack with visual server status and proper controls.

## ✨ Features

- **Visual Server Status**: Red/green indicators show backend and frontend server status
- **External Launch**: Starts MindBack from outside the IDE environment
- **Clean Shutdown**: <PERSON><PERSON><PERSON> stops both servers with one click
- **Browser Integration**: Direct "Open MindBack" button when servers are running
- **Real-time Monitoring**: Continuously monitors server health
- **Windows Native**: Pure Windows solution using Python + tkinter

## 🚀 Quick Setup

Simply double-click `setup_taskbar_icon.bat` - it will:
1. Install required Python dependencies (`requests`, `psutil`)
2. Convert your JPG logo to ICO format
3. Create a desktop shortcut with the MB icon
4. Give you final instructions to pin to taskbar

## 📋 Manual Setup

If you prefer step-by-step:

### Step 1: Install Dependencies
```cmd
pip install -r requirements.txt
```

### Step 2: Convert Logo to ICO
```cmd
python convert_logo_to_ico.py
```

### Step 3: Create the Shortcut
```powershell
powershell -ExecutionPolicy Bypass -File "create_shortcut.ps1"
```

### Step 4: Pin to Taskbar
1. Right-click the "MindBack Launcher" shortcut on Desktop
2. Select "Pin to taskbar"

## 🎯 How It Works

### The GUI Launcher (`launch_mindback.py`):
- **Server Status Monitoring**: Checks ports 8000 (backend) and 5173 (frontend) every 3 seconds
- **Smart Process Management**: Uses `psutil` to properly identify and stop server processes
- **Threaded Operations**: Non-blocking server start/stop operations
- **Error Handling**: Clear error messages and status updates
- **Path Independence**: Automatically finds project root relative to launcher location

### Key Components:
1. **`launch_mindback.py`** - Main GUI launcher with server management
2. **`start_mindback.bat`** - Simple batch wrapper for the Python launcher
3. **`create_shortcut.ps1`** - Creates desktop shortcut with proper paths
4. **`requirements.txt`** - Python dependencies for the launcher

## 🔧 Usage

1. **Start Servers**: Click "Start MindBack" - opens console window showing server startup
2. **Monitor Status**: Watch the red/green dots for server health
3. **Open Browser**: Click "Open MindBack" when frontend is running (green dot)
4. **Stop Servers**: Click "Stop Servers" for clean shutdown

## 🧪 Testing

1. Close any existing MindBack servers
2. Launch the GUI from Desktop shortcut or taskbar
3. Click "Start MindBack" and wait for green status indicators
4. Click "Open MindBack" to launch in browser
5. Test "Stop Servers" for clean shutdown

## 🔍 Troubleshooting

- **Servers won't start**: Check that `run_setup.ps1` exists in project root
- **Status stays red**: Wait 10-20 seconds for servers to fully start
- **Can't stop servers**: Use Task Manager as fallback, then restart launcher
- **Missing dependencies**: Run `pip install -r requirements.txt` in Taskbar folder

## 🎨 What's Different from Before

**Removed Complexity**:
- ❌ No more VBScript wrappers
- ❌ No more hidden execution
- ❌ No more aggressive process killing
- ❌ No more hardcoded paths
- ❌ No more PyInstaller complications

**Added Functionality**:
- ✅ Real-time server status monitoring
- ✅ Visual feedback and error messages
- ✅ Proper process management
- ✅ Clean shutdown capability
- ✅ Browser integration
- ✅ Path-independent operation