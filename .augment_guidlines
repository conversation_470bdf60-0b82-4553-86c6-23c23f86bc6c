# MindBack Development Guidelines
do not run terminal commands to start the app
## Project Overview
- mindback is intended as an AI intrinsic system to moderate intellegence interaction
- the system use the llm to detect the intention of the user (initiatio_prompt2.yaml)
- mindback work with mindbooks and mindsheets in which diffrent templates are used
- feel free to make suggestions where the LLM, ai agents and Model Context Protocol (MCP) can be used

## Core Principles
- Deal with root causes, not symptoms - never use hardcoded fallbacks, validations, placeholders, or workarounds
- Never hardcode variables which should be delivered by the LLM
- Maintain independent workflows for all intention classes
- When debugging, first present an analysis and wait for user feedback
- This application runs on Windows using PowerShell
- when adding code which is replacing other code, the old code has to be marked as deprecated and obsolete

# Dependency Management
- Always manage Python dependencies and requirements requirements.txt` and use a virtual environment.
- Remove unused packages to maintain lean requirements.
- Check for outdated packages using `pip list --outdated`, and validate installs with `pip check`.
- Use version pinning (e.g., `fastapi==0.100.1`) to avoid breaking changes.
- Automate updates using Dependabot or `pip-tools` (`pip-compile`).
- Before modifying `package.json`, review `dependency-notes.md`.

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files in:
  C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\frontend\src\components\MindMap\components\Agents\Prompt_library
- YAML is for human-defined prompt content only. All LLM communication uses JSON.
- LLM calls must ONLY be made from the backend (never frontend).
- Do not change prompts unless clearly instructed by the user.

# Terminal Commands
- Do not execute terminal commands. Instead, provide Windows PowerShell commands for the user to run manually.
- Do not suggest commands for Mac or Linux, Windows only.

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

# LLM Integration
- LLM calls shall be made by the backend only
- The LLM selection box in the Governance box (frontend) is the single source of truth.
- ther shall be no llm model hardcoded in the backend
- All LLM communication and backend/frontend exchange is in JSON only (MBCP).
- Ensure backend logic reflects the frontend selection consistently.
- all prompts are made in .yaml files in the prompt library

# Project Workflow
- 

# Code Style and Communication
- Do not use emojis unless explicitly asked.
- Use TypeScript interfaces (not types), avoid enums, prefer RORO pattern.
- Always communicate decisions and ask for confirmation on implementation.

# Documentation Maintenance
- Follow the established directory structure for different types of documentation
- Update documentation when making significant code changes
- Keep documentation concise, accurate, and up-to-date
- Use Markdown for all documentation files
- Include code examples where appropriate
- Reference specific files and components using relative paths from the project root
