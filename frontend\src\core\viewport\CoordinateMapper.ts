/**
 * CoordinateMapper.ts
 * 
 * Utility for consistent coordinate transformations between different
 * coordinate systems in MindBack mindmap functionality.
 * 
 * Created: 2025-06-24
 * Part of: Phase 1.3 - Coordinate System Unification
 */

import { NodePosition } from './ViewportManager';

export interface StageTransform {
  x: number;
  y: number;
  scaleX: number;
  scaleY: number;
}

export interface ViewportInfo {
  width: number;
  height: number;
  centerX: number;
  centerY: number;
}

/**
 * CoordinateMapper provides utilities for transforming coordinates
 * between different coordinate systems used in the mindmap.
 */
export class CoordinateMapper {
  
  /**
   * Transform stage coordinates to screen coordinates
   * @param stagePos Position in stage coordinate system
   * @param stageTransform Current stage transformation
   * @returns Position in screen coordinate system
   */
  public static stageToScreen(
    stagePos: NodePosition,
    stageTransform: StageTransform
  ): NodePosition {
    return {
      x: (stagePos.x * stageTransform.scaleX) + stageTransform.x,
      y: (stagePos.y * stageTransform.scaleY) + stageTransform.y
    };
  }

  /**
   * Transform screen coordinates to stage coordinates
   * @param screenPos Position in screen coordinate system
   * @param stageTransform Current stage transformation
   * @returns Position in stage coordinate system
   */
  public static screenToStage(
    screenPos: NodePosition,
    stageTransform: StageTransform
  ): NodePosition {
    return {
      x: (screenPos.x - stageTransform.x) / stageTransform.scaleX,
      y: (screenPos.y - stageTransform.y) / stageTransform.scaleY
    };
  }

  /**
   * Transform viewport coordinates to stage coordinates
   * @param viewportPos Position in viewport coordinate system
   * @param stageTransform Current stage transformation
   * @param viewportInfo Viewport information
   * @returns Position in stage coordinate system
   */
  public static viewportToStage(
    viewportPos: NodePosition,
    stageTransform: StageTransform,
    viewportInfo: ViewportInfo
  ): NodePosition {
    // First convert viewport to screen coordinates
    const screenPos = {
      x: viewportPos.x,
      y: viewportPos.y
    };
    
    // Then convert screen to stage coordinates
    return this.screenToStage(screenPos, stageTransform);
  }

  /**
   * Transform stage coordinates to viewport coordinates
   * @param stagePos Position in stage coordinate system
   * @param stageTransform Current stage transformation
   * @param viewportInfo Viewport information
   * @returns Position in viewport coordinate system
   */
  public static stageToViewport(
    stagePos: NodePosition,
    stageTransform: StageTransform,
    viewportInfo: ViewportInfo
  ): NodePosition {
    // First convert stage to screen coordinates
    const screenPos = this.stageToScreen(stagePos, stageTransform);
    
    // Screen coordinates are the same as viewport coordinates in our system
    return screenPos;
  }

  /**
   * Calculate the stage transform needed to center a node in the viewport
   * @param nodePos Position of the node to center
   * @param viewportInfo Viewport information
   * @param currentScale Current scale factor
   * @returns Stage transform that centers the node
   */
  public static calculateCenteringTransform(
    nodePos: NodePosition,
    viewportInfo: ViewportInfo,
    currentScale: number = 1
  ): StageTransform {
    return {
      x: viewportInfo.centerX - (nodePos.x * currentScale),
      y: viewportInfo.centerY - (nodePos.y * currentScale),
      scaleX: currentScale,
      scaleY: currentScale
    };
  }

  /**
   * Check if a stage position is visible in the current viewport
   * @param stagePos Position in stage coordinates
   * @param stageTransform Current stage transformation
   * @param viewportInfo Viewport information
   * @param nodeWidth Width of the node (optional)
   * @param nodeHeight Height of the node (optional)
   * @returns True if the position is visible
   */
  public static isPositionVisible(
    stagePos: NodePosition,
    stageTransform: StageTransform,
    viewportInfo: ViewportInfo,
    nodeWidth: number = 200,
    nodeHeight: number = 100
  ): boolean {
    const screenPos = this.stageToScreen(stagePos, stageTransform);
    
    // Calculate node boundaries in screen coordinates
    const nodeLeft = screenPos.x - nodeWidth / 2;
    const nodeRight = screenPos.x + nodeWidth / 2;
    const nodeTop = screenPos.y - nodeHeight / 2;
    const nodeBottom = screenPos.y + nodeHeight / 2;
    
    // Check if node overlaps with viewport
    return (
      nodeRight >= 0 &&
      nodeLeft <= viewportInfo.width &&
      nodeBottom >= 0 &&
      nodeTop <= viewportInfo.height
    );
  }

  /**
   * Calculate the bounds of all nodes in screen coordinates
   * @param nodePositions Array of node positions in stage coordinates
   * @param stageTransform Current stage transformation
   * @param nodeWidth Default node width
   * @param nodeHeight Default node height
   * @returns Bounding box in screen coordinates
   */
  public static calculateNodeBounds(
    nodePositions: NodePosition[],
    stageTransform: StageTransform,
    nodeWidth: number = 200,
    nodeHeight: number = 100
  ): {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
    width: number;
    height: number;
    center: NodePosition;
  } {
    if (nodePositions.length === 0) {
      return {
        minX: 0,
        maxX: 0,
        minY: 0,
        maxY: 0,
        width: 0,
        height: 0,
        center: { x: 0, y: 0 }
      };
    }

    // Convert all positions to screen coordinates
    const screenPositions = nodePositions.map(pos => 
      this.stageToScreen(pos, stageTransform)
    );

    // Calculate bounds including node dimensions
    const halfWidth = nodeWidth / 2;
    const halfHeight = nodeHeight / 2;

    const minX = Math.min(...screenPositions.map(pos => pos.x - halfWidth));
    const maxX = Math.max(...screenPositions.map(pos => pos.x + halfWidth));
    const minY = Math.min(...screenPositions.map(pos => pos.y - halfHeight));
    const maxY = Math.max(...screenPositions.map(pos => pos.y + halfHeight));

    return {
      minX,
      maxX,
      minY,
      maxY,
      width: maxX - minX,
      height: maxY - minY,
      center: {
        x: (minX + maxX) / 2,
        y: (minY + maxY) / 2
      }
    };
  }

  /**
   * Calculate optimal zoom level to fit all nodes in viewport
   * @param nodePositions Array of node positions in stage coordinates
   * @param viewportInfo Viewport information
   * @param padding Padding around the content
   * @param nodeWidth Default node width
   * @param nodeHeight Default node height
   * @returns Optimal scale factor
   */
  public static calculateFitToViewScale(
    nodePositions: NodePosition[],
    viewportInfo: ViewportInfo,
    padding: number = 50,
    nodeWidth: number = 200,
    nodeHeight: number = 100
  ): number {
    if (nodePositions.length === 0) {
      return 1;
    }

    // Calculate bounds in stage coordinates (before scaling)
    const halfWidth = nodeWidth / 2;
    const halfHeight = nodeHeight / 2;

    const minX = Math.min(...nodePositions.map(pos => pos.x - halfWidth));
    const maxX = Math.max(...nodePositions.map(pos => pos.x + halfWidth));
    const minY = Math.min(...nodePositions.map(pos => pos.y - halfHeight));
    const maxY = Math.max(...nodePositions.map(pos => pos.y + halfHeight));

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    // Calculate available space (viewport minus padding)
    const availableWidth = viewportInfo.width - (padding * 2);
    const availableHeight = viewportInfo.height - (padding * 2);

    // Calculate scale factors for both dimensions
    const scaleX = contentWidth > 0 ? availableWidth / contentWidth : 1;
    const scaleY = contentHeight > 0 ? availableHeight / contentHeight : 1;

    // Use the smaller scale to ensure everything fits
    const scale = Math.min(scaleX, scaleY, 2); // Cap at 2x zoom

    // Ensure minimum scale for readability
    return Math.max(scale, 0.1);
  }

  /**
   * Get viewport information from current window
   * @returns Current viewport information
   */
  public static getCurrentViewportInfo(): ViewportInfo {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      width,
      height,
      centerX: width / 2,
      centerY: height / 2
    };
  }

  /**
   * Normalize a position to ensure it uses consistent coordinate system
   * @param position Position to normalize
   * @param referenceSystem The coordinate system this position is currently in
   * @param targetSystem The coordinate system to convert to
   * @param stageTransform Current stage transformation (if needed)
   * @param viewportInfo Viewport information (if needed)
   * @returns Normalized position in target coordinate system
   */
  public static normalizePosition(
    position: NodePosition,
    referenceSystem: 'stage' | 'screen' | 'viewport',
    targetSystem: 'stage' | 'screen' | 'viewport',
    stageTransform?: StageTransform,
    viewportInfo?: ViewportInfo
  ): NodePosition {
    // If systems are the same, return as-is
    if (referenceSystem === targetSystem) {
      return { ...position };
    }

    // Ensure we have required parameters for transformations
    if (!stageTransform) {
      stageTransform = { x: 0, y: 0, scaleX: 1, scaleY: 1 };
    }
    
    if (!viewportInfo) {
      viewportInfo = this.getCurrentViewportInfo();
    }

    // Convert based on reference and target systems
    switch (`${referenceSystem}->${targetSystem}`) {
      case 'stage->screen':
        return this.stageToScreen(position, stageTransform);
      
      case 'screen->stage':
        return this.screenToStage(position, stageTransform);
      
      case 'stage->viewport':
        return this.stageToViewport(position, stageTransform, viewportInfo);
      
      case 'viewport->stage':
        return this.viewportToStage(position, stageTransform, viewportInfo);
      
      case 'screen->viewport':
      case 'viewport->screen':
        // Screen and viewport are the same in our system
        return { ...position };
      
      default:
        console.warn(`CoordinateMapper: Unsupported transformation ${referenceSystem}->${targetSystem}`);
        return { ...position };
    }
  }
}

// Export utility functions for common operations
export const coordinateUtils = {
  stageToScreen: CoordinateMapper.stageToScreen,
  screenToStage: CoordinateMapper.screenToStage,
  isVisible: CoordinateMapper.isPositionVisible,
  centerNode: CoordinateMapper.calculateCenteringTransform,
  fitToView: CoordinateMapper.calculateFitToViewScale,
  getCurrentViewport: CoordinateMapper.getCurrentViewportInfo,
  normalize: CoordinateMapper.normalizePosition
};
