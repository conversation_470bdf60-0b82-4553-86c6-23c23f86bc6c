/**
 * MindSheet Component
 *
 * A container component that represents a single sheet in the MindBook.
 * Each sheet can contain different types of content (MindMap, ChatFork, etc.)
 * based on the user's intention.
 *
 * IMPORTANT: This component follows <PERSON><PERSON>'s Rules of Hooks carefully:
 * 1. All hooks are called at the top level of the component
 * 2. Helper functions that use state are defined outside the component
 * 3. Event handlers use refs to access the latest state
 * 4. The order of hooks is consistent across renders
 * 5. Lazy-loaded components are defined outside the component
 * 6. Event handlers and complex rendering logic are memoized with useMemo
 *
 * This component follows the Excel model where each sheet is a separate entity
 * with its own content and state.
 */

import React, { useEffect, useState, useRef, useMemo } from 'react';
import { initializeMindMap, processChildNodes } from '../../core/adapters/MindMapAdapter';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import ErrorBoundary from '../../components/ErrorBoundary';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import { getSheetMindMapStore, saveSheetMindMapStoreState } from '../../core/services/StoreService';
import { processMBCPData, MBCPData } from '../../core/mbcp/MBCPProcessor';
import { activateSheet } from '../../core/services/StoreAccessService';
import './MindSheet.css';

// Lazy-loaded components
const LazyMindMapCanvas = React.lazy(() => import('../mindmap/components/Canvas/MindMapCanvasWrapper'));
const LazyChatForkCanvas = React.lazy(() => import('../../components/ChatFork/ChatForkCanvas'));

/**
 * Helper function to initialize a mindmap with content using unified MBCP processor
 * This is a pure function that doesn't use any hooks or call any functions that use hooks
 * It's defined outside the component to avoid React Hooks violations
 */
const initializeMindMapForSheet = (
  store: any, // Store is passed as prop from wrapper component
  mbcpData: any,
  sheetId: string
): boolean => {
  try {
    console.log('MindSheet: Initializing mindmap with unified MBCP processor for sheet:', sheetId);

    if (!mbcpData) {
      console.error('MindSheet: No MBCP data provided');
      return false;
    }

    // Log the MBCP data structure for debugging
    try {
      console.log('MindSheet: MBCP data structure:', JSON.stringify(mbcpData, null, 2));
    } catch (e) {
      console.warn('MindSheet: Could not stringify MBCP data');
    }

    // Get the store state directly
    const storeState = store.getState();

    // Process MBCP data using unified processor
    console.log('MindSheet: Processing MBCP data with unified processor');
    const result = processMBCPData(mbcpData);

    if (!result.success) {
      console.error('MindSheet: Failed to process MBCP data:', result.error);
      return false;
    }

    console.log('MindSheet: Successfully processed MBCP data, root node ID:', result.rootNodeId);

    // Update the layout after a brief delay
    setTimeout(() => {
      console.log('MindSheet: Updating layout for sheet:', sheetId);
      try {
        const preferredStrategy = storeState.getPreferredLayoutStrategy();
        console.log(`MindSheet: Preferred layout strategy for sheet ${sheetId}: ${preferredStrategy}`);
        // Let the user control when layout changes occur
      } catch (error) {
        console.warn('MindSheet: Could not get preferred layout strategy:', error);
      }
    }, 100);

    return true;
  } catch (error) {
    console.error('MindSheet: Error initializing mindmap with unified processor:', error);
    return false;
  }
};

// Content type enum is now imported from StoreTypes.ts

// Props interface
export interface MindSheetProps {
  id: string;
  title: string;
  contentType: MindSheetContentType;
  isActive: boolean;
  content: any; // The content data (MBCP for mindmap, etc.)
  onActivate?: () => void;

  // Store props passed from wrapper
  mindMapStore?: any;
  mindBookStore?: any;
  chatForkStore?: any;
  sheetStore?: any;
}

const MindSheet: React.FC<MindSheetProps> = ({
  id,
  title,
  contentType,
  isActive,
  content,
  onActivate,
  mindMapStore,
  mindBookStore,
  chatForkStore,
  sheetStore: initialSheetStore
}) => {
  // IMPORTANT: The order of hooks must be consistent across all renders
  // Always declare all hooks at the top level in the exact same order

  // 1. useState hooks - All useState hooks must be declared first
  const [initialized, setInitialized] = useState(false);
  const [needsInitialization, setNeedsInitialization] = useState(false);

  // 2. useRef hooks - All useRef hooks must be declared next
  const storeRef = useRef<any>(initialSheetStore);
  const contentRef = useRef<any>(content); // Store content in a ref to avoid unnecessary re-renders
  const savedStateRef = useRef<boolean>(false); // Track if we've saved state to prevent multiple saves

  // 3. useMemo hooks for handlers and components
  // Define all useMemo hooks here to ensure consistent order
  // These hooks must be called in the same order every time
  
  const handleChatForkClose = useMemo(() => () => {
    console.log('ChatFork close requested from MindSheet');
    // Use a local reference to avoid closure issues
    const currentId = id;
    const currentMindBookStore = mindBookStore;
    const currentChatForkStore = chatForkStore;

    // Find another sheet to switch to
    const otherSheet = currentMindBookStore.sheets.find(sheet =>
      sheet.id !== currentId && sheet.contentType === MindSheetContentType.MINDMAP
    );

    if (otherSheet) {
      console.log('MindSheet: Switching to other sheet:', otherSheet.id);
      currentMindBookStore.setActiveSheet(otherSheet.id);
      // Register the sheet switch event
      RegistrationManager.registerEvent(EventType.SHEET_SWITCHED, {
        id: otherSheet.id,
        type: otherSheet.contentType.toLowerCase()
      });
    }

    // Hide the ChatFork
    currentChatForkStore.hideChatFork();
  }, []);

  // Define error handlers with useMemo to ensure consistent hook order
  const handleMindMapError = useMemo(() => (error: Error) => {
    console.error('MindMap Canvas error caught by ErrorBoundary:', error);
    // Log the error to the registration manager
    RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
      component: 'MindMapCanvas',
      message: error.message,
      stack: error.stack
    });
  }, []);

  const handleChatForkError = useMemo(() => (error: Error) => {
    console.error('ChatFork Canvas error caught by ErrorBoundary:', error);
    // Log the error to the registration manager
    RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
      component: 'ChatForkCanvas',
      message: error.message,
      stack: error.stack
    });
  }, []);

  // Update contentRef when content changes
  useEffect(() => {
    contentRef.current = content;
  }, [content]);

  // Initialize the sheet-specific store when the component mounts
  // This effect runs for ALL content types, but only does work for MINDMAP
  useEffect(() => {
    console.log('MindSheet: Component mounted for sheet:', id, 'contentType:', contentType);

    // Store is now passed as a prop from the wrapper
    // Just ensure it's set in the ref
    if (initialSheetStore) {
      storeRef.current = initialSheetStore;
    }

    // Cleanup function to handle component unmount
    return () => {
      // We don't remove the store when the component unmounts
      // because we want to preserve the state between tab switches
      console.log('MindSheet: Component unmounted for sheet:', id);
    };
  }, [id, contentType, initialSheetStore]);

  // Effect for initial mindmap setup - runs once when the component mounts
  useEffect(() => {
    // Only initialize mindmap for MINDMAP content type and when we have a store
    if (contentType === MindSheetContentType.MINDMAP && storeRef.current) {
      console.log('MindSheet: Initial setup for mindmap sheet:', id);

      // Initialize the store with window dimensions
      storeRef.current.getState().initialize(window.innerWidth, window.innerHeight);

      // If we have content on mount, set the flag to initialize
      if (contentRef.current) {
        setNeedsInitialization(true);
      }
    }
  }, [id, contentType]);

  // Separate effect for mindmap initialization based on the needsInitialization flag
  // This ensures initialization happens only when needed and not on every render
  useEffect(() => {
    if (needsInitialization && contentType === MindSheetContentType.MINDMAP && storeRef.current) {
      console.log('MindSheet: Initializing mindmap for sheet:', id);

      // Log the content to help with debugging
      if (contentRef.current) {
        try {
          console.log('MindSheet: Content provided for initialization:',
            typeof contentRef.current === 'object' ?
              JSON.stringify({
                intent: contentRef.current.intent,
                text: contentRef.current.text,
                hasRoot: !!contentRef.current.root,
                hasMindmap: !!contentRef.current.mindmap,
                hasMindmapRoot: !!(contentRef.current.mindmap && contentRef.current.mindmap.root)
              }) :
              typeof contentRef.current
          );
        } catch (e) {
          console.warn('MindSheet: Could not stringify content for logging');
        }
      } else {
        console.warn('MindSheet: No content provided for initialization');
      }

      // Initialize the mindmap with the content
      if (contentRef.current) {
        // We need to use a custom version of initializeMindMap that uses our sheet-specific store
        const success = initializeMindMapForSheet(storeRef.current, contentRef.current, id);
        console.log('MindSheet: Mindmap initialization ' + (success ? 'successful' : 'failed'));

        // Reset the flag
        setNeedsInitialization(false);

        // Set initialized to true
        setInitialized(true);
      } else {
        console.warn('MindSheet: No content available for mindmap initialization');
        setNeedsInitialization(false);
      }
    }
  }, [id, contentType, needsInitialization]);

  // Handle sheet activation/deactivation - this runs for ALL content types
  useEffect(() => {
    if (isActive) {
      console.log('MindSheet: Sheet became active:', id, 'contentType:', contentType);

      // Register the activation event for all content types
      RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
        sheetId: id,
        name: title || `Sheet ${id}`,
        type: contentType.toLowerCase()
      });

      // Add a class to the sheet element to mark it as active
      // This helps with DOM-based active sheet detection
      const sheetElement = document.querySelector(`[data-sheet-id="${id}"]`);
      if (sheetElement) {
        sheetElement.classList.add('active');
      }

      // Ensure all other sheets are marked as inactive
      document.querySelectorAll('.mind-sheet').forEach(element => {
        if (element.getAttribute('data-sheet-id') !== id) {
          element.classList.remove('active');
        }
      });

      // Set this as the active sheet in the MindMapStoreRegistry, but only if it's a mindmap
      // and only after a short delay to prevent freezing
      if (contentType === MindSheetContentType.MINDMAP) {
        setTimeout(() => {
          try {
            // CRITICAL FIX: Use activateSheet to ensure proper activation in all registries
            activateSheet(id);
            console.log('MindSheet: Set as active in all registries:', id);
          } catch (error) {
            console.error('MindSheet: Error setting active sheet in registry:', error);
          }
        }, 100);
      }
    } else {
      console.log('MindSheet: Sheet became inactive:', id);

      // Remove the active class
      const sheetElement = document.querySelector(`[data-sheet-id="${id}"]`);
      if (sheetElement) {
        sheetElement.classList.remove('active');
      }

      // If this is a mindmap sheet, save its state before deactivation
      // but only after a short delay to prevent freezing
      if (contentType === MindSheetContentType.MINDMAP && storeRef.current) {
        // Check if we have nodes to save
        const hasNodes = Object.keys(storeRef.current.getState().nodes || {}).length > 0;
        if (hasNodes) {
          setTimeout(() => {
            try {
              // Use the StoreService to save the sheet state
              saveSheetMindMapStoreState(id);
              console.log('MindSheet: Saved state before deactivation for sheet:', id);
            } catch (error) {
              console.error('MindSheet: Error saving state before deactivation:', error);
            }
          }, 100);
        } else {
          console.log('MindSheet: No nodes to save before deactivation for sheet:', id);
        }
      }

      // Reset the saved state flag when the sheet is deactivated
      savedStateRef.current = false;

      setInitialized(false);
    }
  }, [isActive, id, contentType]);

  // Separate effect for mindmap activation to avoid conditional hook calls
  useEffect(() => {
    if (isActive && contentType === MindSheetContentType.MINDMAP && storeRef.current) {
      console.log('MindSheet: Activating mindmap sheet:', id);

      // First, ensure the store is properly initialized
      const storeState = storeRef.current.getState();

      // Check if the store has nodes
      const hasNodes = Object.keys(storeState.nodes).length > 0;

      // Check if we have saved state in the MindBookStore
      const savedState = mindBookStore?.getSheetState(id);
      const hasSavedState = savedState && savedState.nodes && Object.keys(savedState.nodes).length > 0;

      if (hasSavedState) {
        console.log('MindSheet: Found saved state in MindBookStore for sheet:', id);

        // Apply the saved state to the store
        try {
          // Apply nodes
          Object.entries(savedState.nodes).forEach(([nodeId, node]) => {
            storeState.updateNode(nodeId, node as any);
          });

          // Apply connections
          if (savedState.connections && Array.isArray(savedState.connections)) {
            // Clear existing connections
            storeState.connections.forEach((conn: any) => {
              storeState.deleteConnection(conn.id);
            });

            // Add saved connections
            savedState.connections.forEach((conn: any) => {
              if (conn.from && conn.to) {
                storeState.addConnection(conn.from, conn.to, {
                  color: conn.color,
                  width: conn.width,
                  style: conn.style
                });
              }
            });
          }

          // Apply position and scale
          // SKIP position restoration to let MindMapCanvas centering handle it correctly
          // The saved position might be incorrect due to different screen sizes or previous bugs
          /*
          if (savedState.position) {
            storeState.setPosition(savedState.position);
          }
          */
          console.log('MindSheet: Skipped position restoration to allow proper centering by MindMapCanvas');

          if (savedState.scale) {
            storeState.setScale(savedState.scale);
          }

          // Apply root node ID
          if (savedState.rootNodeId) {
            storeState.rootNodeId = savedState.rootNodeId;
          }

          console.log('MindSheet: Applied saved state to store for sheet:', id);
        } catch (error) {
          console.error('MindSheet: Error applying saved state:', error);
        }
      } else if (!hasNodes) {
        console.log('MindSheet: Store has no nodes, triggering reinitialization for sheet:', id);
        // Reinitialize the store with window dimensions
        storeState.initialize(window.innerWidth, window.innerHeight);

        // Set the flag to trigger initialization in the dedicated effect
        if (contentRef.current) {
          setNeedsInitialization(true);
        }
      }

      // Update layout when sheet becomes active - use a slightly longer delay to ensure DOM is ready
      setTimeout(() => {
        if (storeRef.current) {
          console.log('MindSheet: Sheet activated for sheet:', id);

          try {
            // DO NOT FORCE LAYOUT UPDATE - This was causing layout jumping
            // The layout should only be updated when user explicitly requests it
            // storeRef.current.getState().updateLayout('tree'); // REMOVED - CAUSED JUMPING

            // Get the sheet-specific MindMap store to access the root node ID
            const storeState = storeRef.current.getState();
            const rootNodeId = storeState.rootNodeId;

            if (rootNodeId) {
              // Select the root node
              storeState.selectNode(rootNodeId);

              // Register the node selection event
              RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
                id: rootNodeId,
                sheetId: id
              });

              // Focus the stage for this sheet
              setTimeout(() => {
                // Find the stage container for this sheet
                const sheetElement = document.querySelector(`[data-sheet-id="${id}"]`);
                if (sheetElement) {
                  const stageContainer = sheetElement.querySelector('.konvajs-content');
                  if (stageContainer) {
                    (stageContainer as HTMLElement).focus();
                    console.log('MindSheet: Focused stage for sheet:', id);
                  }
                }
              }, 50);
            }
          } catch (error) {
            console.error('MindSheet: Error during sheet activation:', error);
            RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
              component: 'MindSheet',
              message: `Error during sheet activation: ${error.message}`,
              stack: error.stack
            });
          }
        }
      }, 200);

      // Register the mindmap initialized event
      RegistrationManager.registerEvent(EventType.MINDMAP_INITIALIZED, {
        id,
        type: 'mindmap'
      });

      // Save the state to ensure it's preserved, but only if we have nodes
      // This prevents saving empty state that could cause issues
      // We use the savedStateRef declared at the top level

      if (!savedStateRef.current) {
        setTimeout(() => {
          try {
            // Only save if we have nodes
            if (storeRef.current && Object.keys(storeRef.current.getState().nodes || {}).length > 0) {
              // Save the state to ensure it's preserved when switching sheets
              // Using the StoreService to save the sheet state
              saveSheetMindMapStoreState(id);
              console.log('MindSheet: Saved state after activation for sheet:', id);
              savedStateRef.current = true;
            } else {
              console.log('MindSheet: Not saving state after activation (no nodes) for sheet:', id);
            }
          } catch (error) {
            console.error('MindSheet: Error saving state after activation:', error);
          }
        }, 500);
      }

      setInitialized(true);
    }
  }, [isActive, id, contentType, mindBookStore]);

  // Separate effect for chatfork activation to avoid conditional hook calls
  useEffect(() => {
    if (isActive && contentType === MindSheetContentType.CHATFORK && contentRef.current && chatForkStore) {
      console.log('MindSheet: Activating chatfork sheet:', id);

      // Update the ChatForkStore with this sheet's content and ID
      // Only update if the active sheet ID has changed
      if (chatForkStore.activeSheetId !== id) {
        chatForkStore.showChatFork(contentRef.current, id);
      }
    }
  }, [isActive, id, contentType, chatForkStore]);

  // Listen for refresh_canvas events to force re-renders when needed
  useEffect(() => {
    // Only add the listener if this is an active mindmap sheet
    if (isActive && contentType === MindSheetContentType.MINDMAP) {
      const handleRefreshCanvas = (event: any) => {
        if (event.detail.sheetId === id) {
          console.log('MindSheet: Received refresh_canvas event for sheet:', id);

          // Force a re-render by updating the initialized state
          // This will cause the contentElement to be re-rendered
          setInitialized(false);
          setTimeout(() => {
            setInitialized(true);
            console.log('MindSheet: Forced re-render after refresh event for sheet:', id);
          }, 10);
        }
      };

      // Add event listener
      document.addEventListener('mindback:refresh_canvas', handleRefreshCanvas);

      // Clean up
      return () => {
        document.removeEventListener('mindback:refresh_canvas', handleRefreshCanvas);
      };
    }
  }, [isActive, contentType, id]);

  // Listen for mindmap_data_updated events from MindMapAdapter
  useEffect(() => {
    // This listener is for all mindmap sheets, not just active ones
    if (contentType === MindSheetContentType.MINDMAP) {
      const handleDataUpdated = (event: any) => {
        if (event.detail.sheetId === id && event.detail.hasData) {
          console.log('MindSheet: Received mindmap_data_updated event for sheet:', id);

          // Set flag to trigger initialization
          setNeedsInitialization(true);

          // Check if we should center the view
          if (event.detail.shouldCenterView) {
            // Dispatch an event to center the view after initialization
            setTimeout(() => {
              console.log('MindSheet: Dispatching center-view event');
              const centerEvent = new CustomEvent('mindback:center_view', {
                detail: { sheetId: id }
              });
              document.dispatchEvent(centerEvent);
            }, 500); // Delay to ensure initialization is complete
          }

          // Check if we should collapse the governance box
          if (event.detail.shouldCollapseGovernanceBox) {
            // Dispatch an event to collapse and reposition the governance box
            setTimeout(() => {
              console.log('MindSheet: Dispatching collapse-governance-box event');
              const collapseEvent = new CustomEvent('mindback:collapse_governance_box', {
                detail: {
                  sheetId: id,
                  position: 'top-right'
                }
              });
              document.dispatchEvent(collapseEvent);
            }, 300); // Slightly shorter delay for better UX
          }
        }
      };

      // Add event listener
      document.addEventListener('mindback:mindmap_data_updated', handleDataUpdated);

      // Clean up
      return () => {
        document.removeEventListener('mindback:mindmap_data_updated', handleDataUpdated);
      };
    }
  }, [contentType, id]);

  // All useMemo hooks have been moved to the top level to ensure consistent order

  // Content rendering logic using memoization to avoid hooks violations
  const contentElement = useMemo(() => {
    // Always prepare all possible content elements, but only return the one we need
    // This ensures hooks are always called in the same order

    // Prepare MindMap content
    const mindMapContent = (
      <React.Suspense fallback={<div className="loading-content">Loading MindMap...</div>}>
        <div className="mindmap-container">
          <ErrorBoundary
            componentName="MindMap Canvas"
            onError={handleMindMapError}
          >
            {/* Pass the sheet ID to the canvas so it can use the correct store */}
            <LazyMindMapCanvas
              width={window.innerWidth}
              height={window.innerHeight - 100} // Reduce height to account for header and footer
              sheetId={id}
            />
          </ErrorBoundary>
        </div>
      </React.Suspense>
    );

    // Prepare ChatFork content
    const chatForkContent = (
      <React.Suspense fallback={<div className="loading-content">Loading ChatFork...</div>}>
        <ErrorBoundary
          componentName="ChatFork Canvas"
          onError={handleChatForkError}
        >
          <LazyChatForkCanvas
            content={contentRef.current}
            isVisible={true}
            onClose={handleChatForkClose}
            sheetId={id}
            showHeader={false}
          />
        </ErrorBoundary>
      </React.Suspense>
    );

    // Prepare empty content
    const emptyContent = <div className="empty-sheet">This sheet is empty</div>;

    // Return the appropriate content based on contentType
    switch (contentType) {
      case MindSheetContentType.MINDMAP:
        // If we don't have a sheet store yet, show loading
        if (!storeRef.current) {
          return <div className="loading-content">Initializing MindMap...</div>;
        }
        return mindMapContent;

      case MindSheetContentType.CHATFORK:
        // Log the content to help debug
        if (contentRef.current) {
          console.log('MindSheet: Rendering ChatFork with content:', {
            title: contentRef.current?.text,
            contentLength: contentRef.current?.full_text?.length || 0,
            id,
            isActive
          });
        }
        return chatForkContent;

      case MindSheetContentType.EMPTY:
      default:
        return emptyContent;
    }
  }, [contentType, isActive, id, handleMindMapError, handleChatForkError, handleChatForkClose]);

  // Memoize the MindMapManager component to avoid recreating it on every render
  // We need to ensure this hook is always called in the same order
  const mindMapManager = useMemo(() => {
    // REMOVED: The MindMapManager is now handled exclusively by MindSheetTabs
    // to avoid duplicate instances. MindSheetTabs controls the manager for all sheets.
    return null;
  }, [isActive, contentType, id]);

  return (
    <div
      className={`mind-sheet ${isActive ? 'active' : ''}`}
      onClick={onActivate}
      data-sheet-id={id}
      data-content-type={contentType}
    >
      {contentElement}
    </div>
  );
};

export default MindSheet;